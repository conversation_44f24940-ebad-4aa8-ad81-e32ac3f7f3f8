import { formatJsonWithLineBreaks } from './jsonFormatter';

// Test data that reproduces the issue you found
const testData = {
  "system": "You are an information extraction assistant. Extract the following fields from the email below: 1) Email address of the sender, 2) Name of the sender, 3) Date of Birth (DOB) if present, 4) Any comments or additional information. Return as a JSON object with keys: Email, Name, DOB, Comments. If a field is missing, return an empty string for that field.",
  "snippet": "<PERSON> 19/12/1987 Amazing work bro!",
  "shortField": "test",
  "nestedObject": {
    "description": "This is another long description that should be wrapped to improve readability in the execution inspector",
    "status": "completed"
  }
};

// Test the formatter
console.log("=== Original JSON.stringify ===");
console.log(JSON.stringify(testData, null, 2));

console.log("\n=== Fixed custom formatter ===");
console.log(formatJsonWithLineBreaks(testData));

// The fix ensures:
// 1. Short lines stay on one line: "snippet": "<PERSON> 19/12/1987 Amazing work bro!"
// 2. Long lines break cleanly: "system": "
//    Content continues properly indented...
// 3. No more extra quotes at start of lines
// 4. Valid JSON structure maintained

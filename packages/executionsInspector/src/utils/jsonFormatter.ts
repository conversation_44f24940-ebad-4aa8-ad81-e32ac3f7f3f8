/**
 * Custom JSON formatter that only breaks extremely long text lines (>80 characters)
 * to improve readability without making formatting worse.
 */

export function formatJsonWithLineBreaks(data: any, maxLineLength: number = 80): string {
  if (data === null || data === undefined) {
    return JSON.stringify(data);
  }

  const stringified = JSON.stringify(data, null, 2);
  const lines = stringified.split('\n');
  const result: string[] = [];

  for (const line of lines) {
    const trimmedLine = line.trimEnd();

    // Only process lines that are significantly longer than the limit
    if (trimmedLine.length <= maxLineLength) {
      result.push(trimmedLine);
      continue;
    }

    // Check if it's a key-value pair with a very long string value
    const keyValueMatch = trimmedLine.match(/^(\s*)"([^"]+)":\s*"(.{60,})"(.*)$/);

    if (keyValueMatch) {
      const [, indent, key, stringContent, suffix] = keyValueMatch;

      // Only break if the string is really long (>60 chars)
      if (stringContent.length > 60) {
        const chunks = breakStringIntoChunks(stringContent, 60);

        result.push(`${indent}"${key}": "`);
        chunks.forEach((chunk, index) => {
          const isLast = index === chunks.length - 1;
          result.push(`${indent}  ${chunk}${isLast ? `"${suffix}` : ''}`);
        });
      } else {
        result.push(trimmedLine);
      }
    } else {
      // For other cases, just keep the line as is
      result.push(trimmedLine);
    }
  }

  return result.join('\n');
}

/**
 * Breaks a string into chunks that fit within the specified length,
 * trying to break at word boundaries when possible.
 */
function breakStringIntoChunks(text: string, maxChunkLength: number): string[] {
  if (text.length <= maxChunkLength) {
    return [text];
  }

  const chunks: string[] = [];
  let currentPos = 0;

  while (currentPos < text.length) {
    let chunkEnd = currentPos + maxChunkLength;

    if (chunkEnd >= text.length) {
      // Last chunk
      chunks.push(text.slice(currentPos));
      break;
    }

    // Try to find a good break point (space, punctuation, etc.)
    let breakPoint = chunkEnd;
    const searchStart = Math.max(currentPos, chunkEnd - 20); // Look back up to 20 chars

    for (let i = chunkEnd; i >= searchStart; i--) {
      const char = text[i];
      if (char === ' ' || char === ',' || char === '.' || char === ';' || char === ':' ||
          char === '/' || char === '\\' || char === '-' || char === '_') {
        breakPoint = i + 1; // Include the delimiter
        break;
      }
    }

    // If no good break point found, just break at max length
    if (breakPoint === chunkEnd && breakPoint < text.length) {
      // Avoid breaking in the middle of a word if possible
      while (breakPoint > currentPos && /[a-zA-Z0-9]/.test(text[breakPoint])) {
        breakPoint--;
      }
      if (breakPoint === currentPos) {
        breakPoint = chunkEnd; // Fall back to max length
      }
    }

    chunks.push(text.slice(currentPos, breakPoint));
    currentPos = breakPoint;
  }

  return chunks;
}

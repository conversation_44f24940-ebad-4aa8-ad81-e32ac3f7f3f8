import { createContext, useContext, useState, ReactNode } from 'react';

interface JsonFormattingContextType {
  useCustomFormatting: boolean;
  setUseCustomFormatting: (value: boolean) => void;
}

const JsonFormattingContext = createContext<JsonFormattingContextType | undefined>(undefined);

export function JsonFormattingProvider({ children }: { children: ReactNode }) {
  const [useCustomFormatting, setUseCustomFormatting] = useState(true);

  return (
    <JsonFormattingContext.Provider value={{ useCustomFormatting, setUseCustomFormatting }}>
      {children}
    </JsonFormattingContext.Provider>
  );
}

export function useJsonFormatting() {
  const context = useContext(JsonFormattingContext);
  if (context === undefined) {
    throw new Error('useJsonFormatting must be used within a JsonFormattingProvider');
  }
  return context;
}

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { CheckCircle, AlertCircle, FileText } from 'lucide-react';
import { TaskflowToRawTransformer } from '@/lib/TaskflowToRawTransformer';

interface SchemaInputProps {
  onSchemaChange: (schema: any) => void;
}

export function SchemaInput({ onSchemaChange }: SchemaInputProps) {
  const [schemaText, setSchemaText] = useState('{\n  "nodes": [\n    {\n      "id": "trigger1",\n      "type": "trigger.syncTrigger",\n      "parameters": {\n        "providerKey": "example",\n        "model": "ExampleModel"\n      }\n    }\n  ]\n}');
  const [validationResult, setValidationResult] = useState<{ success: boolean; error?: string } | null>(null);
  const [isValidating, setIsValidating] = useState(false);

  const validateSchema = async () => {
    setIsValidating(true);
    try {
      const parsed = JSON.parse(schemaText);
      
      // Basic validation - check for required properties
      if (!parsed.nodes || !Array.isArray(parsed.nodes)) {
        throw new Error('Schema must have a "nodes" array');
      }
      
      // Validate each node has required properties
      for (const node of parsed.nodes) {
        if (!node.id || !node.type) {
          throw new Error('Each node must have "id" and "type" properties');
        }
      }

      setValidationResult({ success: true });
      onSchemaChange(parsed);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Invalid JSON format';
      setValidationResult({ success: false, error: errorMessage });
      onSchemaChange(null);
    } finally {
      setIsValidating(false);
    }
  };

  const handleSchemaTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setSchemaText(e.target.value);
    setValidationResult(null); // Clear validation when text changes
  };

  const convertToRaw = () => {
    try {
      const parsed = JSON.parse(schemaText);
      const transformer = new TaskflowToRawTransformer(parsed);
      const raw = transformer
        .mergeTriggersIntoNodes()
        .removeConnectProviderSteps()
        .getSchema();
      setSchemaText(JSON.stringify(raw, null, 2));
    } catch {
      // ignore errors, keep current text
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Taskflow Schema Input</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Taskflow Schema (JSON)</label>
          <Textarea
            value={schemaText}
            onChange={handleSchemaTextChange}
            placeholder="Enter your taskflow schema here..."
            className="min-h-[200px] font-mono text-xs"
            autoFormatJson
          />
        </div>
        
        <div className="flex items-center space-x-4">
          <Button
            onClick={validateSchema}
            disabled={isValidating}
            size="sm"
          >
            {isValidating ? 'Validating...' : 'Validate Schema'}
          </Button>
          <Button onClick={convertToRaw} variant="secondary" size="sm">
            Convert to Raw
          </Button>
          
          {validationResult && (
            <div className="flex items-center space-x-2 text-sm">
              {validationResult.success ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                  <span className="text-green-700 dark:text-green-300">Schema is valid</span>
                </>
              ) : (
                <>
                  <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                  <span className="text-red-700 dark:text-red-300">{validationResult.error}</span>
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Settings, Play, AlertCircle } from 'lucide-react';
import { evaluateNodeParameters } from '../lib/jsonLogicUtils';
import { formatJsonWithLineBreaks } from '@/utils/jsonFormatter';
import { useJsonFormatting } from '@/contexts/JsonFormattingContext';

interface NodeParameterEvaluatorProps {
  schema: any;
}

export function NodeParameterEvaluator({ schema }: NodeParameterEvaluatorProps) {
  const [selectedNodeId, setSelectedNodeId] = useState('');
  const [inputData, setInputData] = useState('{\n  "input": {},\n  "trigger": {}\n}');
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [isEvaluating, setIsEvaluating] = useState(false);
  const { useCustomFormatting } = useJsonFormatting();

  // Convert nodes array to array of node IDs for easier access
  const nodes = schema?.nodes || [];
  const nodeIds = nodes.map((node: any) => node.id);
  const selectedNode = nodes.find((node: any) => node.id === selectedNodeId);

  const evaluateParameters = async () => {
    if (!selectedNode || !inputData.trim()) {
      setError('Please select a node and provide input data');
      return;
    }

    setIsEvaluating(true);
    setError(null);

    try {
      const parsedInput = JSON.parse(inputData);
      const evaluatedParams = evaluateNodeParameters(selectedNode.parameters || {}, parsedInput);
      setResult(evaluatedParams);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Evaluation failed';
      setError(errorMessage);
    } finally {
      setIsEvaluating(false);
    }
  };

  const handleInputDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputData(e.target.value);
    setResult(null);
    setError(null);
  };

  if (!schema) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-8">
          <Settings className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Schema Available</h3>
          <p className="text-muted-foreground text-center">
            Please load a valid taskflow schema to evaluate node parameters.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>Node Parameter Evaluator</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Select Node</label>
          <Select value={selectedNodeId} onValueChange={setSelectedNodeId}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a node to evaluate..." />
            </SelectTrigger>
            <SelectContent>
              {nodeIds.map((nodeId: string) => {
                const node = nodes.find((n: any) => n.id === nodeId);
                return (
                  <SelectItem key={nodeId} value={nodeId}>
                    {nodeId} ({node?.type || 'unknown'})
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Input Data (JSON)</label>
          <Textarea
            value={inputData}
            onChange={handleInputDataChange}
            placeholder="Enter input data for parameter evaluation..."
            className="min-h-[100px] font-mono text-xs"
            autoFormatJson
          />
        </div>

        <div className="flex items-center space-x-4">
          <Button
            onClick={evaluateParameters}
            disabled={isEvaluating || !selectedNodeId}
            size="sm"
          >
            <Play className="h-4 w-4 mr-2" />
            {isEvaluating ? 'Evaluating...' : 'Evaluate Parameters'}
          </Button>
        </div>

        {selectedNode?.parameters && (
          <div className="p-3 bg-muted rounded-md">
            <h4 className="text-sm font-medium mb-2">Node Parameters:</h4>
            <pre className="text-xs overflow-auto">
              {useCustomFormatting ? formatJsonWithLineBreaks(selectedNode.parameters) : JSON.stringify(selectedNode.parameters, null, 2)}
            </pre>
          </div>
        )}

        {result && (
          <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
            <h4 className="text-sm font-medium mb-2 text-green-700 dark:text-green-300">
              Evaluation Result:
            </h4>
            <pre className="text-xs overflow-auto text-green-800 dark:text-green-200">
              {useCustomFormatting ? formatJsonWithLineBreaks(result) : JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        {error && (
          <div className="flex items-start space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5" />
            <div className="text-sm text-red-700 dark:text-red-300">{error}</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

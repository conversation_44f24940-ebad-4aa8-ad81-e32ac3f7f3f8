import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter, Play, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import { matchCondition } from '../lib/jsonLogicUtils';
import { formatJsonWithLineBreaks } from '@/utils/jsonFormatter';
import { useJsonFormatting } from '@/contexts/JsonFormattingContext';

interface ConditionMatcherProps {
  schema: any;
}

export function ConditionMatcher({ schema }: ConditionMatcherProps) {
  const [selectedTriggerId, setSelectedTriggerId] = useState<string>('');
  const [testData, setTestData] = useState('{\n  "field": "value"\n}');
  const [result, setResult] = useState<boolean | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isMatching, setIsMatching] = useState(false);
  const { useCustomFormatting } = useJsonFormatting();

  // Convert nodes array to triggers
  const nodes = schema?.nodes || [];
  const triggerNodes = nodes.filter((node: any) => node.type?.includes('trigger'));
  const triggerIds = triggerNodes.map((node: any) => node.id);

  const selectedTrigger = triggerNodes.find((node: any) => node.id === selectedTriggerId);
  const condition = selectedTrigger?.condition;

  const testCondition = async () => {
    if (!condition || !testData.trim()) {
      setError('Please select a trigger with a condition and provide test data');
      return;
    }

    setIsMatching(true);
    setError(null);
    setResult(null);

    try {
      const parsedData = JSON.parse(testData);
      const matchResult = matchCondition(parsedData, condition);
      setResult(matchResult);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Condition matching failed';
      setError(errorMessage);
    } finally {
      setIsMatching(false);
    }
  };

  const handleTestDataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTestData(e.target.value);
    setResult(null);
    setError(null);
  };

  if (!schema) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12 text-center">
          <Filter className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Schema Available</h3>
          <p className="text-muted-foreground">
            Please input and validate a taskflow schema first.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Filter className="h-5 w-5" />
          <span>Condition Matcher</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Select Trigger</label>
          <Select value={selectedTriggerId} onValueChange={setSelectedTriggerId}>
            <SelectTrigger>
              <SelectValue placeholder="Choose a trigger to test..." />
            </SelectTrigger>
            <SelectContent>
              {triggerIds.length > 0 ? (
                triggerIds.map((triggerId: string) => (
                  <SelectItem key={triggerId} value={triggerId}>
                    {triggerId}
                  </SelectItem>
                ))
              ) : (
                <SelectItem value="default" disabled>
                  No triggers found in schema
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        {selectedTrigger && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Trigger Condition</label>
            {condition ? (
              <div className="bg-muted p-3 rounded text-xs font-mono overflow-auto max-h-32">
                <pre>{useCustomFormatting ? formatJsonWithLineBreaks(condition) : JSON.stringify(condition, null, 2)}</pre>
              </div>
            ) : (
              <div className="bg-muted p-3 rounded text-xs text-muted-foreground">
                No condition found for this trigger
              </div>
            )}
          </div>
        )}

        <div className="space-y-2">
          <label className="text-sm font-medium">Test Data (JSON)</label>
          <Textarea
            value={testData}
            onChange={handleTestDataChange}
            placeholder="Enter test data to match against the condition..."
            className="min-h-[150px] font-mono text-xs"
            autoFormatJson
          />
        </div>

        <div className="flex items-center space-x-4">
          <Button
            onClick={testCondition}
            disabled={isMatching || !selectedTriggerId || !condition}
            size="sm"
          >
            <Play className="h-4 w-4 mr-2" />
            {isMatching ? 'Testing...' : 'Test Condition'}
          </Button>
        </div>

        {error && (
          <div className="flex items-center space-x-2 text-sm text-red-700 dark:text-red-300">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}

        {result !== null && (
          <div className="space-y-2">
            <label className="text-sm font-medium">Match Result</label>
            <div className={`flex items-center space-x-2 text-sm p-3 rounded ${
              result
                ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
            }`}>
              {result ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  <span>Condition matches the test data</span>
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4" />
                  <span>Condition does not match the test data</span>
                </>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

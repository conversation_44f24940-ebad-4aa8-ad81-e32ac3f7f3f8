import { strictEqual } from 'node:assert';
import { test } from 'node:test';
import { Nan<PERSON> } from '@nangohq/node';
import fs from 'node:fs';
import { fetchAccessible } from './fetchAccessible';
import dotenv from 'dotenv';
dotenv.config();

function initNango() {
  const secretKey = process.env.NANGO_SECRET_KEY;
  if (!secretKey) {
    throw new Error('NANGO_SECRET_KEY not set');
  }
  return new Nango({ secretKey });
}

test('fetchAccessible retrieves file content', async () => {
  const nango = initNango();
  const file = {
    "url": "drive/v3/files/1syfshTXN0DoITmpjvwXH1YVAlk5jW_34AtWkNObgZrs/export?mimeType=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "authentication": {
      "providerKey": "google-drive",
      "connectionId": "c6fbf6d5-fb81-4138-aca9-979d1739633e"
    },
    "responseType": "arraybuffer",
    "fileName": "Test Spreadsheet.xlsx"
  };
  const result = await fetchAccessible(nango as any, file);

  fs.writeFileSync(
    file.fileName,
    result as Buffer,
    'binary'
  );
});

import type { NangoAction, HarvestProjectInput, HarvestDeleteProjectOutput } from '../../models';
import { getHarvestAccountId } from '../utils/harvestHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

/**
 * Deletes a project in Harvest.
 *
 * @param {Object} input - The input parameters
 * @param {number} input.project_id - The ID of the project to delete
 * @returns {Promise<HarvestDeleteProjectOutput>} The result of the deletion operation
 */
export default async function runAction(
  nango: NangoAction,
  input: HarvestProjectInput
): Promise<HarvestDeleteProjectOutput | NangoError> {
  try {
    if (!input.project_id) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Missing required parameter: project_id is required',
        },
      };
    }

    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }

    const fetchResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/v2/projects/${input.project_id}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    await nango.proxy({
      method: 'DELETE',
      endpoint: `/v2/projects/${input.project_id}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    return {
      project: fetchResponse.data,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error('Error deleting project from Harvest:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while deleting the project.';

    return { error: { status, message } };
  }
}

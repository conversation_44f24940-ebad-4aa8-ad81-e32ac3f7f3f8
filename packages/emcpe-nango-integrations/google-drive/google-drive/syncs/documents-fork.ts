import type { NangoSync, Document, ProxyConfiguration } from '../../models';

interface GoogleScopeSyncMetadata {
  folders?: string[];
  files?: string[];
}

interface GoogleDriveFileResponse {
  id: string;
  name: string;
  fileExtension?: string;
  mimeType: string;
  webViewLink: string;
  parents?: string[];
  modifiedTime: string;
  exportLinks: Record<string, string>;
  size: string;
  description: string;
}

const BATCH_SIZE = 100;

/**
 * Sync Google Drive documents and folders: either recursively from root (default) or for metadata specified folders/files.
 */
async function fetchData(nango: NangoSync): Promise<void> {
  const metadata = (await nango.getMetadata<GoogleScopeSyncMetadata>()) || {};
  const initialFolders = metadata.folders?.length ?? 0 > 0 ? [...metadata.folders!] : ['root'];
  const processedFolders = new Set<string>();
  const batch: Document[] = [];

  for (const folderId of initialFolders) {
    await walkFolder(nango, folderId, processedFolders, batch);
  }

  if (metadata?.files) {
    for (const fileId of metadata.files) {
      await processExpectedFile(nango, fileId, batch);
    }
  }

  if (batch.length > 0) {
    await nango.batchSave<Document>(batch, 'Document');
  }
}

/**
 * Recursively walks through the Google Drive folder structure, processing each file and folder.
 */
async function walkFolder(
  nango: NangoSync,
  folderId: string,
  processedFolders: Set<string>,
  batch: Document[]
): Promise<void> {
  if (processedFolders.has(folderId)) return;
  processedFolders.add(folderId);

  const query = `('${folderId}' in parents) and trashed = false`;
  const proxyConfiguration: ProxyConfiguration = {
    endpoint: `drive/v3/files`,
    params: {
      fields:
        'files(id, name, fileExtension, mimeType, webViewLink, parents, modifiedTime, exportLinks, md5Checksum, size, description), nextPageToken',
      pageSize: BATCH_SIZE.toString(),
      corpora: 'allDrives',
      includeItemsFromAllDrives: 'true',
      supportsAllDrives: 'true',
      q: query,
    },
    paginate: { response_path: 'files' },
    retries: 10,
  };

  for await (const files of nango.paginate<GoogleDriveFileResponse>(proxyConfiguration)) {
    for (const file of files) {
      if (file.mimeType === 'application/vnd.google-apps.folder') {
        await walkFolder(nango, file.id, processedFolders, batch);
      } else {
        const document = createDocument(nango, file);
        batch.push(document);

        if (batch.length === BATCH_SIZE) {
          await nango.batchSave<Document>(batch, 'Document');
          batch.length = 0;
        }
      }
    }
  }
}

/**
 * Only used if a specific named file - don't even understand this use case (except for updates to known files)
 */
async function processExpectedFile(
  nango: NangoSync,
  fileId: string,
  batch: Document[]
): Promise<void> {
  try {
    const config: ProxyConfiguration = {
      endpoint: `drive/v3/files/${fileId}`,
      params: {
        fields:
          'id, name, fileExtension, mimeType, webViewLink, parents, modifiedTime, exportLinks, md5Checksum, size, description',
        supportsAllDrives: 'true',
      },
      retries: 10,
    };

    const { data } = await nango.get<GoogleDriveFileResponse>(config);
    const document = createDocument(nango, data);
    batch.push(document);

    if (batch.length === BATCH_SIZE) {
      await nango.batchSave<Document>(batch, 'Document');
      batch.length = 0;
    }
  } catch (e: any) {
    await nango.log(`Error fetching file ${fileId}: ${e}`, { level: 'error' });
  }
}

function createDocument(nango: NangoSync, file: GoogleDriveFileResponse): Document {
  const googleMapping = googleDocsMimeTypeMapping[file.mimeType];
  const ext =
    file.fileExtension || googleMapping?.extension || lookthroughMimeTypeMapping[file.mimeType];
  const downloadUrl =
    googleMapping?.exportLinks?.[googleMapping.mimeType] ||
    (googleMapping
      ? `drive/v3/files/${file.id}/export?mimeType=${googleMapping.mimeType}`
      : `drive/v3/files/${file.id}?alt=media&supportsAllDrives=true`);
  const fileName =
    ext && !file.name.toLowerCase().endsWith(`.${ext.toLowerCase()}`)
      ? `${file.name}.${ext}`
      : file.name;

  return {
    id: file.id,
    url: file.webViewLink,
    mimeType: file.mimeType,
    title: file.name,
    updatedAt: file.modifiedTime,
    size: file.size,
    description: file.description,
    accessible: {
      url: downloadUrl,
      authentication: {
        providerKey: 'google-drive',
        connectionId: nango.connectionId,
      },
      responseType: 'arraybuffer',
      fileName,
    },
  };
}

interface MimeTypeMapping {
  mimeType: string;
  extension: string;
  exportLinks?: Record<string, string>;
}

export const googleDocsMimeTypeMapping: Record<string, MimeTypeMapping> = {
  'application/vnd.google-apps.document': {
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    extension: 'docx',
  },
  'application/vnd.google-apps.document-rtf': {
    mimeType: 'application/rtf',
    extension: 'rtf',
  },
  'application/vnd.google-apps.spreadsheet': {
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    extension: 'xlsx',
  },
  'application/vnd.google-apps.presentation': {
    mimeType: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    extension: 'pptx',
  },
  'application/vnd.google-apps.drawing': {
    mimeType: 'image/jpeg',
    extension: 'jpg',
  },
  'application/vnd.google-apps.script': {
    mimeType: 'application/vnd.google-apps.script+json',
    extension: 'json',
  },
};

export const lookthroughMimeTypeMapping: Record<string, string> = {
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  'application/vnd.oasis.opendocument.text': 'odt',
  'application/rtf': 'rtf',
  'application/pdf': 'pdf',
  'text/plain': 'txt',
  'application/zip': 'zip',
  'application/epub+zip': 'epub',
  'text/markdown': 'md',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'application/vnd.oasis.opendocument.spreadsheet': 'ods',
  'text/csv': 'csv',
  'text/tab-separated-values': 'tsv',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
  'application/vnd.oasis.opendocument.presentation': 'odp',
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/svg+xml': 'svg',
};

export default fetchData;

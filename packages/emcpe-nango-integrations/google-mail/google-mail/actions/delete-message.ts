import type {
  NangoA<PERSON>,
  GmailDeleteMessageOutput,
  GmailMessageIdInput,
  GmailMessage,
} from '../../models';
import getMessage from './get-message';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailMessageIdInput
): Promise<GmailDeleteMessageOutput | NangoError> {
  const { messageId } = input;

  if (!messageId) {
    return {
      error: {
        status: 400,
        message: 'Input validation failed: Message ID is required to delete a message.',
      },
    };
  }

  try {
    const fetched = await getMessage(nango, { id: messageId, format: 'full' });
    if ((fetched as any).error) {
      return fetched as NangoError;
    }

    await nango.proxy({
      method: 'DELETE',
      endpoint: `/gmail/v1/users/me/messages/${messageId}`,
    });

    return {
      message: fetched as GmailMessage,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error('Failed to delete message:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while deleting the message.';
    return { error: { status, message } };
  }
}

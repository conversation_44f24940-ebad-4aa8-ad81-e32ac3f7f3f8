import type { Attachments, GmailEmailLabelTriggered, NangoSync } from '../../models';
import type { Schema$Message, Schema$MessagePart } from '../types';

export default async function fetchData(nango: NangoSync) {
  const connectionId = nango.connectionId;
  const metadata = await nango.getMetadata<{ historyId?: string }>();
  let startHistoryId = metadata?.historyId;

  // If no historyId exists, fetch it from the user's profile (first run)
  if (!startHistoryId) {
    const profileData = await nango.proxy({
      method: 'GET',
      endpoint: '/gmail/v1/users/me/profile',
      connectionId,
      providerConfigKey: 'google-mail',
      retries: 10,
    });
    startHistoryId = profileData.data.historyId as string;
  }

  let nextPageToken: string | undefined;
  const emails: GmailEmailLabelTriggered[] = [];

  do {
    const params: Record<string, string> = { startHistoryId };
    if (nextPageToken) {
      params['pageToken'] = nextPageToken;
    }

    // Fetch history of changes since the last historyId
    const historyResponse = await nango.proxy({
      method: 'GET',
      endpoint: '/gmail/v1/users/me/history',
      params,
      retries: 10,
    });

    // Fetch all labels to map IDs to names
    const labelsResponse = await nango.proxy({
      method: 'GET',
      endpoint: '/gmail/v1/users/me/labels',
      retries: 10,
    });
    const labelMap = new Map<string, string>(
      labelsResponse.data.labels.map((label: any) => [label.id, label.name.toUpperCase()])
    );

    const historyList = historyResponse.data.history || [];
    for (const history of historyList) {
      const { labelsAdded } = history;

      // Process only entries where labels were added
      if (labelsAdded?.length) {
        const message = labelsAdded[0].message;
        const messageId = message.id;

        // Fetch full email details
        const messageDetail = await nango.proxy<Schema$Message>({
          method: 'GET',
          endpoint: `/gmail/v1/users/me/messages/${messageId}`,
          retries: 10,
        });

        const headers =
          messageDetail.data.payload?.headers?.reduce(
            (acc: any, curr: any) => ({ ...acc, [curr.name]: curr.value }),
            {}
          ) || {};

        emails.push(mapEmail(messageDetail.data, headers, labelMap, nango));
      }
    }

    // Save emails with labels added
    if (emails.length) {
      await nango.batchSave(emails, 'GmailEmailLabelTriggered');
      emails.length = 0; // Clear array after saving
    }

    nextPageToken = historyResponse.data.nextPageToken;
    if (!nextPageToken && historyResponse.data.historyId) {
      await nango.updateMetadata({
        historyId: historyResponse.data.historyId,
      });
    }
  } while (nextPageToken);
}

function processParts(
  parts: Schema$MessagePart[],
  bodyObj: { plain: string; html: string },
  attachments: Attachments[],
  payload?: Schema$MessagePart // Add payload as an optional param
): void {
  for (const part of parts) {
    if (part.mimeType === 'text/plain' && part.body?.data) {
      bodyObj.plain = Buffer.from(part.body.data, 'base64').toString('utf8');
    } else if (part.mimeType === 'text/html' && part.body?.data) {
      bodyObj.html = Buffer.from(part.body.data, 'base64').toString('utf8');
    } else if (part.filename && part.body?.attachmentId) {
      if (part.mimeType && part.body?.size !== undefined && part.body?.size !== null) {
        attachments.push({
          filename: part.filename,
          mimeType: part.mimeType,
          size: part.body.size,
          attachmentId: part.body.attachmentId,
        });
      }
    }
    if (part.parts?.length) {
      processParts(part.parts, bodyObj, attachments);
    }
  }
  // If no parts, check the root payload
  if (!parts.length && payload?.body?.data) {
    if (payload.mimeType === 'text/plain') {
      bodyObj.plain = Buffer.from(payload.body.data, 'base64').toString('utf8');
    } else if (payload.mimeType === 'text/html') {
      bodyObj.html = Buffer.from(payload.body.data, 'base64').toString('utf8');
    }
  }
}

function mapEmail(
  messageDetail: Schema$Message,
  headers: Record<string, any>,
  labelMap: Map<string, string>,
  nango: NangoSync
): GmailEmailLabelTriggered {
  const parts = messageDetail.payload?.parts || [];
  const bodyObj = { plain: '', html: '' };
  const attachments: Attachments[] = [];

  processParts(parts, bodyObj, attachments, messageDetail.payload); // Pass payload here

  const mappedAttachments = attachments.map(att => ({
    ...att,
    accessible: {
      url: `/gmail/v1/users/me/messages/${messageDetail.id}/attachments/${att.attachmentId}`,
      authentication: {
        providerKey: 'google-mail',
        connectionId: nango.connectionId,
      },
      responseType: 'json',
      responseDataPath: 'data',
      responseEncoding: 'base64',
    },
  }));

  return {
    id: messageDetail.id,
    sender: headers['From'],
    recipients: headers['To'],
    date: new Date(parseInt(messageDetail.internalDate)).toISOString(),
    subject: headers['Subject'],
    body: bodyObj.html || bodyObj.plain,
    attachments: mappedAttachments,
    threadId: messageDetail.threadId,
    isDraft: messageDetail.labelIds?.includes('DRAFT') || false,
    labels: (messageDetail.labelIds || []).map(id => labelMap.get(id) || id),
    snippet: messageDetail.snippet || '',
    cc: headers['Cc'] || '',
    bcc: headers['Bcc'] || '',
    messageId: headers['Message-ID'] || '',
    inReplyTo: headers['In-Reply-To'] || '',
    references: headers['References'] || '',
  };
}

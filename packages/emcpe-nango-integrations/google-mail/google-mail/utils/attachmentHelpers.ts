import type { NangoAction, UrlAccessibleFile } from '../../models';
import { fetchAccessible } from './fetchAccessible';

export async function buildAttachmentLines(
  nango: NangoAction,
  attachments: UrlAccessibleFile[],
  boundary: string
): Promise<string[]> {
  const lines: string[] = [];

  for (const file of attachments) {
    try {
      const buffer = await fetchAccessible(nango, file);
      const b64 = buffer.toString('base64');
      const mime = 'application/octet-stream';
      const filename = file.url.split('/').pop() || 'attachment';

      lines.push(`--${boundary}`);
      lines.push(`Content-Type: ${mime}`);
      lines.push(`Content-Disposition: attachment; filename="${filename}"`);
      lines.push('Content-Transfer-Encoding: base64');
      lines.push('');
      lines.push(b64);
    } catch (err) {
      console.error('Error fetching attachment:', err);
    }
  }

  return lines;
}

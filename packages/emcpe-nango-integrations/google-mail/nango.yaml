integrations:
    google-mail:
        syncs:
            emails-fork:
                runs: every 1 minutes
                description: |
                    Fetches a list of emails from Gmail. Defaults to 1-day backfill,
                    adjustable via `backfillPeriodMs` in milliseconds.
                input: OptionalBackfillSetting
                version: 1.0.4
                scopes:
                    - https://www.googleapis.com/auth/gmail.readonly
                output: GmailEmail
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /emails-fork
                    group: Emails
            emails-labels-added:  # New sync for labels added
                runs: every 1 minutes
                description: |
                    Tracks emails where labels have been added using Gmail History API.
                    Stores historyId in metadata for incremental syncing.
                version: 1.0.0
                scopes:
                    - https://www.googleapis.com/auth/gmail.readonly
                output: GmailEmailLabelTriggered
                sync_type: incremental
                endpoint:
                    method: GET
                    path: /labels-added
                    group: Emails
        actions:
            compose-draft:
              endpoint: POST /draft
              description: Creates a new draft email in Gmail.
              input: GmailDraftInput
              output: GmailDraftOutput
              scopes:
                - https://www.googleapis.com/auth/gmail.modify
            compose-draft-reply:
                description: Creates a new draft email that is a reply to an existing email.
                output: GmailReplyDraftOutput
                endpoint: POST /draft-reply
                scopes: https://www.googleapis.com/auth/gmail.modify
                input: GmailReplyDraftInput
            list-messages:
                endpoint: GET /list-messages
                description: Lists emails from Gmail inbox with optional filtering.
                input: GmailListMessagesInput
                output: GmailMessageList
                scopes:
                  - https://www.googleapis.com/auth/gmail.readonly
            get-message:
                endpoint: GET /get-message
                description: Retrieves a specific email by ID.
                input: GmailGetMessageInput
                output: GmailMessage
                scopes:
                  - https://www.googleapis.com/auth/gmail.readonly
            send-email:
                endpoint: POST /send-email
                description: Sends an email via Gmail.
                input: GmailSendEmailInput
                output: GmailSendEmailOutput
                scopes:
                  - https://www.googleapis.com/auth/gmail.send
            modify-message-labels:
                endpoint: POST /gmail/v1/users/me/messages/:messageId/modify # Simplified endpoint
                description: Modifies the labels applied to a specific message.
                input: GmailModifyMessageLabelsInput
                output: GmailMessage # Reverted to full model
                scopes:
                  - https://www.googleapis.com/auth/gmail.modify # Requires modify scope
            trash-message:
                endpoint: POST /gmail/v1/users/me/messages/:messageId/trash # Simplified endpoint
                description: Moves the specified message to the trash.
                input: GmailMessageIdInput
                output: GmailMessage # Reverted to full model
                scopes:
                  - https://www.googleapis.com/auth/gmail.modify # Requires modify scope
            untrash-message:
                endpoint: POST /gmail/v1/users/me/messages/:messageId/untrash # Simplified endpoint
                description: Removes the specified message from the trash.
                input: GmailMessageIdInput
                output: GmailMessage # Reverted to full model
                scopes:
                  - https://www.googleapis.com/auth/gmail.modify # Requires modify scope
            delete-message:
                endpoint: DELETE /gmail/v1/users/me/messages/:messageId # Simplified endpoint
                description: Permanently deletes the specified message. Bypasses Trash.
                input: GmailMessageIdInput
                output: GmailDeleteMessageOutput # Returns empty on success
                scopes:
                  - https://www.googleapis.com/auth/gmail.modify # Requires modify scope
            create-filter:
                endpoint: POST /gmail/v1/users/me/settings/filters
                description: Creates an email filter in Gmail.
                input: GmailCreateFilterInput
                output: GmailFilter
                scopes:
                  - https://www.googleapis.com/auth/gmail.modify

models:
    # Sync-related models
    GmailEmail:
        id: string
        sender: string
        recipients: string
        date: string
        subject: string
        body: string
        attachments: Attachments[]
        threadId: string
        isDraft: boolean
        labels: string[]
        snippet: string
        cc: string
        bcc: string
        messageId: string
        inReplyTo: string
        references: string
    GmailEmailLabelTriggered:
        id: string
        sender: string
        recipients: string
        date: string
        subject: string
        body: string
        attachments: Attachments[]
        threadId: string
        isDraft: boolean
        labels: string[]
        snippet: string
        cc: string
        bcc: string
        messageId: string
        inReplyTo: string
        references: string
    Attachments:
        filename: string
        mimeType: string
        size: number
        attachmentId: string
        accessible?: UrlAccessibleFile
    OptionalBackfillSetting:
        backfillPeriodMs?: number
    # Action-related models
    GmailDraftInput:
      recipient: string
      subject: string
      body?: string
      headers?: object
      attachments?: UrlAccessibleFile[]
    GmailDraftOutput:
      id: string
      threadId: string | null
    GmailReplyDraftInput:
        sender: string          # From GmailEmail: sender (e.g., "<EMAIL>")
        subject: string         # From GmailEmail: subject
        body: string            # From GmailEmail: body
        threadId: string        # From GmailEmail: threadId
        messageId: string       # From GmailEmail: messageId for threading
        inReplyTo: string       # From GmailEmail: inReplyTo (may be empty)
        references: string      # From GmailEmail: references (may be empty)
        date: string            # From GmailEmail: date
        replyBody: string       # Your new reply text
    GmailReplyDraftOutput:
        id: string
        threadId: string | null
    GmailListMessagesInput:
        maxResults?: number     # Maximum number of messages to return (default: 10)
        labelIds?: string[]     # Only return messages with these labels
        q?: string              # Optional search query
        pageToken?: string      # Page token for pagination
    GmailMessageList:
        messages: GmailBasicMessageDetails[]
        nextPageToken?: string  # Token for getting the next page of results
    GmailBasicMessageDetails:
        id: string              # Message ID
        threadId: string        # Thread ID
        labelIds?: string[]     # Labels applied to the message
        snippet?: string        # Short snippet of the message content
        subject?: string        # Email subject
        date?: string           # Email date
    GmailGetMessageInput:
        id: string              # Message ID to retrieve
        format?: string         # Format of the message (full, metadata, minimal, raw)
    # Main message model with flat structure to avoid cyclic references
    GmailMessage:
        id: string              # Message ID
        threadId: string        # Thread ID
        labelIds?: string[]     # Labels applied to the message
        snippet?: string        # Short snippet of the message content
        payload?: object        # Made optional
        sizeEstimate?: number   # Made optional
        historyId?: string      # Made optional
        internalDate?: string   # Made optional
        # Extracted data for convenience
        headers?: GmailHeader[] # Extracted headers
        body?: string          # Decoded body content
        mimeType?: string      # MIME type of the message
        filename?: string      # Filename (if attachment)
        attachments?: GmailAttachmentInfo[] # Information about attachments

    # Header structure
    GmailHeader:
        name: string            # Header name
        value: string           # Header value

    # Attachment information
    GmailAttachmentInfo:
        filename: string        # Attachment filename
        mimeType: string        # Attachment MIME type
        size: number            # Size in bytes
        attachmentId?: string   # Attachment ID for retrieval

    GmailMessageIdInput:
        messageId: string       # The ID of the message to target

    GmailModifyMessageLabelsInput:
        messageId: string       # The ID of the message to modify
        addLabelIds?: string[]  # List of label IDs to add
        removeLabelIds?: string[] # List of label IDs to remove

    GmailDeleteMessageOutput:
        message: GmailMessage   # Details of the deleted message
        deletedAt: string       # ISO timestamp when the deletion occurred

    GmailSearchMessagesInput:
        q: string               # Search query
        maxResults?: number     # Maximum number of messages to return (default: 10)
        labelIds?: string[]     # Only return messages with these labels
        pageToken?: string      # Page token for pagination
    GmailSendEmailInput:
        to: string              # Recipient email address
        subject: string         # Email subject
        body: string            # Email body
        from?: string           # Sender email (if different from authenticated user)
        cc?: string             # CC recipients
        bcc?: string            # BCC recipients
        attachments?: UrlAccessibleFile[] # Optional attachments
        GmailAttachment:
            filename: string        # Attachment filename
            content: string         # Base64 encoded content
            mimeType: string        # MIME type of the attachment
    GmailSendEmailOutput:
        id: string              # Message ID
        threadId: string        # Thread ID
        labelIds?: string[]     # Labels applied to the message

    UrlAuthentication:
        providerKey: string
        connectionId: string

    UrlAccessibleFile:
        url: string
        authentication?: UrlAuthentication
        responseType?: string # json | arraybuffer | text
        responseDataPath?: string
        responseEncoding?: string # base64 | binary | utf8 | hex | ascii | latin1 | ucs2 | utf16le


    GmailFilterCriteria:
        from?: string
        to?: string
        subject?: string
        query?: string
        negatedQuery?: string
        hasAttachment?: boolean
        size?: number
        sizeComparison?: string

    GmailFilterAction:
        addLabelIds?: string[]
        removeLabelIds?: string[]
        forward?: string

    GmailCreateFilterInput:
        criteria: GmailFilterCriteria
        action: GmailFilterAction

    GmailFilter:
        id: string
        criteria: GmailFilterCriteria
        action: GmailFilterAction

integrations:
    google-sheet:
        actions:
            create-sheet:
                endpoint: POST /create-sheet
                description: Creates a new Google Sheet with optional initial data.
                input: GoogleSheetCreateInput
                output: GoogleSheetCreateOutput
                scopes:
                  - https://www.googleapis.com/auth/spreadsheets
                  - https://www.googleapis.com/auth/drive.file
            edit-sheet:
                endpoint: POST /edit-sheet
                description: Updates an existing Google Sheet with new data.
                input: GoogleSheetUpdateInput
                output: GoogleSheetUpdateOutput
                scopes:
                  - https://www.googleapis.com/auth/spreadsheets
                  - https://www.googleapis.com/auth/drive.file
            append-sheet:
                endpoint: POST /append-sheet
                description: Appends values to the next row of a table in a Google Sheet.
                input: GoogleSheetAppendInput
                output: GoogleSheetAppendOutput
                scopes:
                  - https://www.googleapis.com/auth/spreadsheets
                  - https://www.googleapis.com/auth/drive.file


models:
    GoogleSheetCreateInput:
        title: string           # The title of the new spreadsheet
        sheets?: GoogleSheetTab[] # Optional array of sheet tabs to create
    GoogleSheetTab:
        title: string           # The title of the sheet tab
        data?: SheetData        # Optional data for the sheet
    SheetData:
        rows: SheetRow[]        # Array of rows
    SheetRow:
        cells: string[]         # Array of cell values as strings
    GoogleSheetCreateOutput:
        id: string              # The ID of the created spreadsheet
        url: string             # The URL to access the spreadsheet
        title: string           # The title of the spreadsheet
    GoogleSheetUpdateInput:
        spreadsheetId: string   # The ID of the spreadsheet to update
        updates: SheetUpdate[]  # Array of updates to apply
    SheetUpdate:
        sheetId?: number        # Optional sheet ID (if not provided, will use index)
        sheetName?: string      # Optional sheet name (if not provided, will use sheetId)
        range?: string          # Optional A1 notation range (e.g., "A1:C10")
        startRow?: number       # Optional zero-based row index to start update
        startColumn?: number    # Optional zero-based column index to start update
        data: SheetData         # Data to update in the sheet
    GoogleSheetUpdateOutput:
        spreadsheetId: string   # The ID of the updated spreadsheet
        updatedRange: string    # The range that was updated
        updatedRows: number     # Number of rows updated
        updatedColumns: number  # Number of columns updated
        updatedCells: number    # Total number of cells updated
        updatedData?: GoogleSheetUpdatedData
    GoogleSheetUpdatedData:
        range: string           # The range of the updated data
        majorDimension: string # The major dimension of the data (e.g., "ROWS" or "COLUMNS")
        values:
          - string[]          # 2D array of updated value
    GoogleSheetAppendInput:
        spreadsheetId: string   # The ID of the spreadsheet to update
        range: string           # The A1 notation of the range to search for a table
        values:
          - string[]            # 2D array of values to append
    GoogleSheetAppendOutput:
        spreadsheetId: string   # The spreadsheet the updates were applied to
        tableRange: string      # The range of the table before values were appended
        updates: GoogleSheetUpdateOutput # Information about the appended values

integrations:
    linear:
        actions:
            # Issue CRUD actions
            list-issues:
                endpoint: GET /list-issues
                description: Lists issues from Linear.
                input: LinearIssuesInput
                output: LinearIssueList
            get-issue:
                endpoint: GET /get-issue
                description: Gets a specific issue by ID.
                input: LinearIssueInput
                output: LinearIssue
            create-issue:
                endpoint: POST /create-issue
                description: Creates a new issue in Linear.
                input: LinearCreateIssueInput
                output: LinearIssue
            update-issue:
                endpoint: PATCH /update-issue
                description: Updates an existing issue in Linear.
                input: LinearUpdateIssueInput
                output: LinearIssue
            delete-issue:
                endpoint: DELETE /delete-issue
                description: Deletes an issue in Linear.
                input: LinearIssueInput
                output: LinearDeleteIssueOutput
            # Project CRUD actions
            list-projects:
                endpoint: GET /list-projects
                description: List all projects from Linear
                input: LinearProjectsInput
                output: LinearProjectList
            get-project:
                endpoint: GET /get-project
                description: Gets a specific project by ID.
                input: LinearProjectInput
                output: LinearProject
            create-project:
                endpoint: POST /create-project
                description: Creates a new project in Linear.
                input: LinearCreateProjectInput
                output: LinearProject
            update-project:
                endpoint: PATCH /update-project
                description: Updates an existing project in Linear.
                input: LinearUpdateProjectInput
                output: LinearProject
            # Team CRUD actions
            list-teams:
                endpoint: GET /list-teams
                description: Lists teams from Linear.
                input: LinearTeamsInput
                output: LinearTeamList
            get-team:
                endpoint: GET /get-team
                description: Gets a specific team by ID.
                input: LinearTeamInput
                output: LinearTeam

models:
    # Linear models
    LinearIssuesInput:
        teamId?: string         # Optional team ID to filter issues by
        projectId?: string      # Optional project ID to filter issues by
        states?: string[]       # Optional list of state IDs to filter issues by
        assigneeId?: string     # Optional assignee ID to filter issues by
        priority?: number       # Optional priority to filter issues by
        sortBy?: string         # Optional field to sort by
        sortOrder?: string      # Optional sort order (asc or desc)
        limit?: number          # Optional limit on the number of issues to return
        first?: number          # Optional number of items to return (for pagination)
        after?: string          # Optional cursor for pagination

    LinearIssueInput:
        issueId: string      # ID of the issue to retrieve

    LinearUser:
        id: string           # User ID
        name: string         # User name
        email: string        # User email
        displayName?: string # User display name
        avatarUrl?: string | null # URL to user's avatar (nullable)

    LinearState:
        id: string           # State ID
        name: string         # State name
        color: string        # State color
        type: string         # State type (e.g., 'unstarted', 'started', 'completed')

    LinearTeamBasic:
        id: string           # Team ID
        name: string         # Team name
        key: string          # Team key (shorthand code)

    LinearProjectBasic:
        id: string           # Project ID
        name: string         # Project name
        icon?: string        # Project icon
        color?: string       # Project color

    LinearIssue:
        id: string              # Issue ID
        title: string           # Issue title
        description?: string    # Issue description
        number: number          # Issue number
        priority: number        # Issue priority
        url: string             # Issue URL
        createdAt: string       # Issue creation date
        updatedAt: string       # Issue last update date
        state: LinearState      # Issue state
        assignee?: LinearUser   # Issue assignee
        team: LinearTeamBasic   # Issue team
        project?: LinearProjectBasic # Issue project
        labels?: LinearLabel[]  # Issue labels assigned to the issue

    LinearIssueList:
        issues: LinearIssue[]   # List of issues
        pageInfo?: PageInfo     # Pagination information

    LinearLabel:
        id: string           # Label ID
        name: string         # Label name
        color: string        # Label color

    LinearCreateIssueInput:
        teamId: string       # ID of the team to create the issue in
        title: string        # Issue title
        description?: string # Optional issue description
        stateId?: string     # Optional state ID
        assigneeId?: string  # Optional assignee ID
        priority?: number    # Optional priority
        projectId?: string   # Optional project ID
        labelIds?: string[]  # Optional array of label IDs

    LinearUpdateIssueInput:
        issueId: string      # ID of the issue to update
        title?: string       # Optional new title
        description?: string # Optional new description
        stateId?: string     # Optional new state ID
        assigneeId?: string  # Optional new assignee ID
        priority?: number    # Optional new priority
        projectId?: string   # Optional new project ID
        labelIds?: string[]  # Optional new array of label IDs

    LinearDeleteIssueOutput:
        issue: LinearIssue    # The deleted issue
        deletedAt: string     # ISO timestamp when the deletion occurred

    PageInfo:
        hasNextPage: boolean  # Whether there are more items
        endCursor?: string    # Cursor to the last item in the current page

    LinearTeamInput:
        teamId: string       # ID of the team to retrieve

    LinearTeam:
        id: string           # Team ID
        name: string         # Team name
        key: string          # Team key (shorthand code)
        description?: string | null # Team description (nullable)
        color?: string       # Team color
        private?: boolean    # Whether the team is private
        createdAt: string    # When the team was created
        updatedAt: string    # When the team was last updated
        members: LinearUser[] # Team members

    LinearTeamList:
        teams: LinearTeam[]  # List of teams
        pageInfo?: PageInfo  # Pagination information

    LinearTeamsInput:      # Empty model for list-teams action
        first?: number      # Optional number of teams to return
        after?: string      # Optional cursor for pagination

    LinearProjectsInput:
        first?: number       # Optional number of items to return (for pagination)
        after?: string       # Optional cursor for pagination

    LinearProjectInput:
        projectId: string    # ID of the project to retrieve

    # User model used in projects
    LinearUserBasic:
        id: string           # User ID
        name: string         # User name
        email?: string       # User email

    LinearProject:
        id: string           # Project ID
        name: string         # Project name
        description?: string # Project description
        url?: string         # Project URL
        color?: string       # Project color
        state: string        # Project state (e.g., 'planned', 'started', 'completed')
        lead?: LinearUserBasic # Project lead (nullable)
        teams: LinearTeamBasic[] # Teams associated with the project
        createdAt: string    # When the project was created
        updatedAt: string    # When the project was last updated

    LinearProjectList:
        projects: LinearProject[] # List of projects
        pageInfo?: PageInfo      # Pagination information

    LinearCreateProjectInput:
        name: string         # Project name
        description?: string # Optional project description
        icon?: string        # Optional project icon
        color?: string       # Optional project color
        teamIds: string[]    # IDs of teams to associate with the project

    LinearUpdateProjectInput:
        projectId: string    # ID of the project to update
        name?: string        # Optional new name
        description?: string # Optional new description
        icon?: string        # Optional new icon
        color?: string       # Optional new color
        state?: string       # Optional new state
        teamIds?: string[]   # Optional new team IDs

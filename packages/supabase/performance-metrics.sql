WITH user_message_metrics AS (
  SELECT
    c.id AS conversation_id,
    (idx + 1) AS message_index,
    msg->>'id' AS message_id,
    msg->>'content' AS user_prompt,
    ((msg->'metrics'->>'fullLoopEnd')::bigint - (msg->'metrics'->>'clientSentAt')::bigint) AS total_response_ms,
    ((msg->'metrics'->>'serverReceivedAt')::bigint - (msg->'metrics'->>'clientSentAt')::bigint) AS network_latency_ms,
    ((msg->'metrics'->>'validatedAndAuthenticatedAt')::bigint - (msg->'metrics'->>'serverReceivedAt')::bigint) AS validation_ms,
    ((msg->'metrics'->>'prepStart')::bigint - (msg->'metrics'->>'validatedAndAuthenticatedAt')::bigint) AS prep_start_ms,
    ((msg->'metrics'->>'upsertEnd')::bigint - (msg->'metrics'->>'prepStart')::bigint) AS upsert_ms,
    ((msg->'metrics'->>'contextEnd')::bigint - (msg->'metrics'->>'upsertEnd')::bigint) AS context_prep_ms,
    ((msg->'metrics'->>'firstBeforeLlmCallAt')::bigint - (msg->'metrics'->>'contextEnd')::bigint) AS loop_start_ms,
    ((msg->'metrics'->>'firstMessageChunkAt')::bigint - (msg->'metrics'->>'firstBeforeLlmCallAt')::bigint) AS message_chunk_latency_ms,
    ((msg->'metrics'->>'firstFirstChunkAt')::bigint - (msg->'metrics'->>'firstMessageChunkAt')::bigint) AS first_chunk_latency_ms,
    ((msg->'metrics'->>'firstLastChunkAt')::bigint - (msg->'metrics'->>'firstFirstChunkAt')::bigint) AS streaming_duration_ms,
    COALESCE((
      SELECT array_agg(
        ((ac->>'lastChunkAt')::bigint - (ac->>'beforeLlmCallAt')::bigint)
      )
      FROM jsonb_array_elements(msg->'metrics'->'agentCalls') AS ac
      WHERE ac->>'beforeLlmCallAt' IS NOT NULL
        AND ac->>'lastChunkAt' IS NOT NULL
    ), '{}') AS loop_durations_ms
  FROM conversations c,
    LATERAL jsonb_array_elements(c.messages) WITH ORDINALITY AS m(msg, idx)
  WHERE msg->>'role' = 'user'
    AND msg ? 'metrics'
    AND msg->'metrics'->>'clientSentAt' IS NOT NULL
    AND msg->'metrics'->>'serverReceivedAt' IS NOT NULL
    AND msg->'metrics'->>'validatedAndAuthenticatedAt' IS NOT NULL
    AND msg->'metrics'->>'prepStart' IS NOT NULL
    AND msg->'metrics'->>'upsertEnd' IS NOT NULL
    AND msg->'metrics'->>'contextEnd' IS NOT NULL
    AND msg->'metrics'->>'firstBeforeLlmCallAt' IS NOT NULL
    AND msg->'metrics'->>'firstMessageChunkAt' IS NOT NULL
    AND msg->'metrics'->>'firstFirstChunkAt' IS NOT NULL
    AND msg->'metrics'->>'firstLastChunkAt' IS NOT NULL
    AND msg->'metrics'->>'fullLoopEnd' IS NOT NULL
)
SELECT
  conversation_id,
  message_index,
  message_id,
  user_prompt,
  total_response_ms,
  network_latency_ms,
  validation_ms,
  prep_start_ms,
  upsert_ms,
  context_prep_ms,
  loop_start_ms,
  message_chunk_latency_ms,
  first_chunk_latency_ms,
  streaming_duration_ms,
  loop_durations_ms,
  NULL::bigint AS median_loop_duration_ms,
  NULL::bigint AS mean_loop_duration_ms,
  NULL::bigint AS min_loop_duration_ms,
  NULL::bigint AS max_loop_duration_ms,
  0 AS sort_order
FROM user_message_metrics

UNION ALL

SELECT
  'AGGREGATE' AS conversation_id,
  NULL AS message_index,
  'MEDIAN'  AS message_id,
  NULL AS user_prompt,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_response_ms)::bigint AS total_response_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY network_latency_ms)::bigint AS network_latency_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY validation_ms)::bigint AS validation_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY prep_start_ms)::bigint AS prep_start_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY upsert_ms)::bigint AS upsert_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY context_prep_ms)::bigint AS context_prep_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY loop_start_ms)::bigint AS loop_start_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY message_chunk_latency_ms)::bigint AS message_chunk_latency_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY first_chunk_latency_ms)::bigint AS first_chunk_latency_ms,
  PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY streaming_duration_ms)::bigint AS streaming_duration_ms,
  NULL AS loop_durations_ms,
  (SELECT PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY ld)::bigint FROM user_message_metrics, unnest(loop_durations_ms) AS ld WHERE ld IS NOT NULL) AS median_loop_duration_ms,
  NULL::bigint AS mean_loop_duration_ms,
  NULL::bigint AS min_loop_duration_ms,
  NULL::bigint AS max_loop_duration_ms,
  -4 AS sort_order
FROM user_message_metrics

UNION ALL

SELECT
  'AGGREGATE' AS conversation_id,
  NULL AS message_index,
  'MEAN'  AS message_id,
  NULL AS user_prompt,
  AVG(total_response_ms)::bigint AS total_response_ms,
  AVG(network_latency_ms)::bigint AS network_latency_ms,
  AVG(validation_ms)::bigint AS validation_ms,
  AVG(prep_start_ms)::bigint AS prep_start_ms,
  AVG(upsert_ms)::bigint AS upsert_ms,
  AVG(context_prep_ms)::bigint AS context_prep_ms,
  AVG(loop_start_ms)::bigint AS loop_start_ms,
  AVG(message_chunk_latency_ms)::bigint AS message_chunk_latency_ms,
  AVG(first_chunk_latency_ms)::bigint AS first_chunk_latency_ms,
  AVG(streaming_duration_ms)::bigint AS streaming_duration_ms,
  NULL AS loop_durations_ms,
  NULL::bigint AS median_loop_duration_ms,
  (SELECT AVG(ld)::bigint FROM user_message_metrics, unnest(loop_durations_ms) AS ld WHERE ld IS NOT NULL) AS mean_loop_duration_ms,
  NULL::bigint AS min_loop_duration_ms,
  NULL::bigint AS max_loop_duration_ms,
  -3 AS sort_order
FROM user_message_metrics

UNION ALL

SELECT
  'AGGREGATE' AS conversation_id,
  NULL AS message_index,
  'MIN'  AS message_id,
  NULL AS user_prompt,
  MIN(total_response_ms)::bigint AS total_response_ms,
  MIN(network_latency_ms)::bigint AS network_latency_ms,
  MIN(validation_ms)::bigint AS validation_ms,
  MIN(prep_start_ms)::bigint AS prep_start_ms,
  MIN(upsert_ms)::bigint AS upsert_ms,
  MIN(context_prep_ms)::bigint AS context_prep_ms,
  MIN(loop_start_ms)::bigint AS loop_start_ms,
  MIN(message_chunk_latency_ms)::bigint AS message_chunk_latency_ms,
  MIN(first_chunk_latency_ms)::bigint AS first_chunk_latency_ms,
  MIN(streaming_duration_ms)::bigint AS streaming_duration_ms,
  NULL AS loop_durations_ms,
  NULL::bigint AS median_loop_duration_ms,
  NULL::bigint AS mean_loop_duration_ms,
  (SELECT MIN(ld)::bigint FROM user_message_metrics, unnest(loop_durations_ms) AS ld WHERE ld IS NOT NULL) AS min_loop_duration_ms,
  NULL::bigint AS max_loop_duration_ms,
  -2 AS sort_order
FROM user_message_metrics

UNION ALL

SELECT
  'AGGREGATE' AS conversation_id,
  NULL AS message_index,
  'MAX'  AS message_id,
  NULL AS user_prompt,
  MAX(total_response_ms)::bigint AS total_response_ms,
  MAX(network_latency_ms)::bigint AS network_latency_ms,
  MAX(validation_ms)::bigint AS validation_ms,
  MAX(prep_start_ms)::bigint AS prep_start_ms,
  MAX(upsert_ms)::bigint AS upsert_ms,
  MAX(context_prep_ms)::bigint AS context_prep_ms,
  MAX(loop_start_ms)::bigint AS loop_start_ms,
  MAX(message_chunk_latency_ms)::bigint AS message_chunk_latency_ms,
  MAX(first_chunk_latency_ms)::bigint AS first_chunk_latency_ms,
  MAX(streaming_duration_ms)::bigint AS streaming_duration_ms,
  NULL AS loop_durations_ms,
  NULL::bigint AS median_loop_duration_ms,
  NULL::bigint AS mean_loop_duration_ms,
  NULL::bigint AS min_loop_duration_ms,
  (SELECT MAX(ld)::bigint FROM user_message_metrics, unnest(loop_durations_ms) AS ld WHERE ld IS NOT NULL) AS max_loop_duration_ms,
  -1 AS sort_order
FROM user_message_metrics
ORDER BY sort_order, conversation_id, message_index;

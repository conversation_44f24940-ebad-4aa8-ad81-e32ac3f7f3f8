import dotenv from 'dotenv';
dotenv.config();
import { actionsAgent } from './netlify/functions/_agents/actionsAgent';
import { vercel } from './netlify/functions/_protocol/vercel';

const metrics: Record<string, any> = {};

async function doWork() {
  metrics.clientSentAt = Date.now();

  const stream = actionsAgent([vercel.userMessage('Halp! I need to test your response times!')], {
    userDateTime: new Date().toISOString(),
  });

  const decoder = new TextDecoder();

  // 4. Interpret & send response
  for await (const chunk of stream.toDataStream()) {
    const text = decoder.decode(chunk, { stream: true });

    if (text.startsWith('f:')) {
      metrics.messageChunk = Date.now();
    }

    if (metrics.firstChunkAt === undefined && !text.startsWith('f:')) {
      metrics.firstChunkAt = Date.now();
    }
    metrics.lastChunkAt = Date.now();
  }

  metrics.serverReceivedAt = Date.now();

  console.log('Metrics:', {
    messageChunkTime: metrics.messageChunk - metrics.clientSentAt,
    firstChunkTime: metrics.firstChunkAt - metrics.clientSentAt,
    lastChunkTime: metrics.lastChunkAt - metrics.clientSentAt,
    streamDuration: metrics.serverReceivedAt - metrics.firstChunkAt,
    totalTime: metrics.serverReceivedAt - metrics.clientSentAt,
  });
}

doWork();

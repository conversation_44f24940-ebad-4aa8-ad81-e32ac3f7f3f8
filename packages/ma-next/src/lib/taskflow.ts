import { supabase } from './supabase';
import { invokeFunction } from 'utils/invokeFunction';

/**
 * Subscribes to updates for a taskflow execution
 */
function subscribeToExecutionUpdates(
  executionId: string,
  callback: (execution: Record<string, any>) => void
) {
  const subscription = supabase
    .channel(`taskflow-execution-${executionId}`)
    .on(
      'postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public',
      table: 'taskflow_executions',
      filter: `id=eq.${executionId}`,
    },
    async () => {
      try {
        const updated = await fetchTaskflowExecution(executionId);
        callback(updated);
      } catch {
        // ignore fetch errors in subscription handler
      }
    }
  )
  .subscribe();

  return subscription;
}

/**
 * Fetches a taskflow execution by ID
 */
async function fetchTaskflowExecution(executionId: string) {
  const { data, error } = await invokeFunction(
    `get-execution?id=${executionId}`,
    { method: 'GET' }
  );

  if (error) {
    console.error('Error fetching taskflow execution:', error);
    throw error;
  }

  return data;
}

async function fetchTaskflowExecutions(taskflowId: string) {
  const { data, error } = await invokeFunction(
    `list-executions?taskflowId=${taskflowId}`,
    { method: 'GET' }
  );
  if (error) {
    console.error('Error fetching executions:', error);
    throw error;
  }
  return data as any[];
}

async function fetchExecutionTrace(executionId: string) {
  const { data, error } = await invokeFunction(
    `get-execution-trace?id=${executionId}`,
    { method: 'GET' }
  );
  if (error) {
    console.error('Error fetching execution trace:', error);
    throw error;
  }
  return data as any;
}

async function updateTaskflowExecution(
  executionId: string,
  updates: Record<string, any>
) {
  const { error } = await invokeFunction('update-execution', {
    body: { executionId, updates },
  });
  if (error) {
    console.error('Error updating execution:', error);
    throw error;
  }
}

/**
 * Resumes a paused taskflow execution
 */
async function resumeTaskflowExecution(
  executionId: string,
  nodeId: string,
  data: Record<string, any>,
  // Default to true to replay subsequent nodes
  force: boolean = true
): Promise<Record<string, any>> {
  try {
    const { data: result, error } = await invokeFunction('resume-taskflow', {
      body: {
        executionId,
        nodeId,
        data,
        force,
      },
    });

    if (error) {
      throw new Error(error.message || 'Failed to resume taskflow');
    }

    return result;
  } catch (error) {
    console.error('Error resuming taskflow:', error);
    throw error;
  }
}

export {
  fetchTaskflowExecution,
  fetchTaskflowExecutions,
  updateTaskflowExecution,
  resumeTaskflowExecution,
  subscribeToExecutionUpdates,
  fetchExecutionTrace,
};

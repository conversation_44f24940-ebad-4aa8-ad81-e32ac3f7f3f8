// Auto-generated by scripts/nangoIntrospection.ts on 2025-06-07T02:14:49.649Z
// LLM, AGENTS, ___NEVER____ _____EVER_____ UPDATE THIS FILE UNDER ANY CIRCUMSTANCES

export const ACTION_INPUTS_KEYED = {
  "harvest:add-historical-time-entry": "HarvestAddHistoricalTimeEntryInput",
  "harvest:create-client": "HarvestCreateClientInput",
  "harvest:create-project": "HarvestCreateProjectInput",
  "harvest:delete-project": "HarvestProjectInput",
  "harvest:delete-time-entry": "HarvestTimeEntryInput",
  "harvest:get-client": "HarvestClientInput",
  "harvest:get-project": "HarvestProjectInput",
  "harvest:get-time-entry": "HarvestTimeEntryInput",
  "harvest:list-projects": "HarvestProjectsInput",
  "harvest:list-project-tasks": "HarvestProjectTasksInput",
  "harvest:list-tasks": "HarvestTasksInput",
  "harvest:list-time-entries": "HarvestTimeEntriesInput",
  "harvest:restart-timer": "HarvestTimeEntryInput",
  "harvest:start-timer": "HarvestStartTimerInput",
  "harvest:stop-timer": "HarvestTimeEntryInput",
  "harvest:update-time-entry": "HarvestUpdateTimeEntryInput",
  "github:add-pull-request-review-comment": "GithubAddPullRequestReviewCommentInput",
  "github:create-issue": "GithubCreateIssueInput",
  "github:create-organization-repository": "GithubCreateOrganizationRepositoryInput",
  "github:create-pull-request": "GithubCreatePullRequestInput",
  "github:create-pull-request-review": "GithubCreatePullRequestReviewInput",
  "github:create-repository": "GithubCreateRepositoryInput",
  "github:delete-repository": "GithubRepositoryInput",
  "github:get-issue": "GithubIssueInput",
  "github:get-pull-request": "GithubPullRequestInput",
  "github:get-pull-request-comments": "GithubPullRequestInput",
  "github:get-pull-request-files": "GithubPullRequestInput",
  "github:get-pull-request-status": "GithubPullRequestInput",
  "github:get-repository": "GithubRepositoryInput",
  "github:list-branches": "GithubListBranchesInput",
  "github:list-issues": "GithubIssuesInput",
  "github:list-pull-requests": "GithubListPullRequestsInput",
  "github:merge-pull-request": "GithubMergePullRequestInput",
  "github:update-issue": "GithubUpdateIssueInput",
  "github:update-pull-request": "GithubUpdatePullRequestInput",
  "github:update-pull-request-branch": "GithubUpdatePullRequestBranchInput",
  "github:update-repository": "GithubUpdateRepositoryInput",
  "github:write-file": "GithubWriteFileInput",
  "slack:add-reaction-as-user": "SlackAddReactionInput",
  "slack:create-channel": "SlackCreateChannelInput",
  "slack:delete-message-as-user": "SlackDeleteMessageInput",
  "slack:get-channel-history": "SlackGetChannelHistoryInput",
  "slack:get-message-permalink": "SlackGetPermalinkInput",
  "slack:get-user-info": "SlackGetUserInfoInput",
  "slack:list-channels": "SlackListChannelsInput",
  "slack:search-messages": "SlackSearchMessagesInput",
  "slack:send-message-as-user": "SlackSendMessageInput",
  "slack:update-message-as-user": "SlackUpdateMessageInput",
  "google-sheet:append-sheet": "GoogleSheetAppendInput",
  "google-sheet:create-sheet": "GoogleSheetCreateInput",
  "google-sheet:edit-sheet": "GoogleSheetUpdateInput",
  "google-calendar:create-event": "GoogleCalendarEventInput",
  "google-calendar:delete-event": "GoogleCalendarEventDeleteInput",
  "google-calendar:list-events": "GoogleCalendarEventsInput",
  "google-calendar:update-event": "GoogleCalendarEventUpdateInput",
  "google-mail:compose-draft": "GmailDraftInput",
  "google-mail:compose-draft-reply": "GmailReplyDraftInput",
  "google-mail:create-filter": "GmailCreateFilterInput",
  "google-mail:delete-message": "GmailMessageIdInput",
  "google-mail:get-message": "GmailGetMessageInput",
  "google-mail:list-messages": "GmailListMessagesInput",
  "google-mail:modify-message-labels": "GmailModifyMessageLabelsInput",
  "google-mail:send-email": "GmailSendEmailInput",
  "google-mail:trash-message": "GmailMessageIdInput",
  "google-mail:untrash-message": "GmailMessageIdInput",
  "dropbox:copy-file": "DropboxCopyInput",
  "dropbox:create-folder": "DropboxCreateFolderInput",
  "dropbox:delete-file": "DropboxDeleteInput",
  "dropbox:get-file": "DropboxGetFileInput",
  "dropbox:list-files": "DropboxListFilesInput",
  "dropbox:move-file": "DropboxMoveInput",
  "dropbox:search-files": "DropboxSearchInput",
  "dropbox:upload-file": "DropboxUploadFileInput",
  "notion:create-database": "NotionCreateDatabaseInput",
  "notion:create-page": "NotionCreatePageInput",
  "notion:get-database": "NotionGetDatabaseInput",
  "notion:get-page": "NotionGetPageInput",
  "notion:query-database": "NotionQueryDatabaseInput",
  "notion:search": "NotionSearchInput",
  "notion:update-database": "NotionUpdateDatabaseInput",
  "notion:update-page": "NotionUpdatePageInput",
  "google-docs:create-document": "GoogleDocsCreateDocumentInput",
  "google-docs:get-document": "GoogleDocsGetDocumentInput",
  "google-docs:update-document": "GoogleDocsUpdateDocumentInput",
  "linear:create-issue": "LinearCreateIssueInput",
  "linear:create-project": "LinearCreateProjectInput",
  "linear:delete-issue": "LinearIssueInput",
  "linear:get-issue": "LinearIssueInput",
  "linear:get-project": "LinearProjectInput",
  "linear:get-team": "LinearTeamInput",
  "linear:list-issues": "LinearIssuesInput",
  "linear:list-projects": "LinearProjectsInput",
  "linear:list-teams": "LinearTeamsInput",
  "linear:update-issue": "LinearUpdateIssueInput",
  "linear:update-project": "LinearUpdateProjectInput",
  "google-drive:list-documents": "ListDocumentsInput",
  "twitter-v2:send-post": "XSocialPostInput",
  "linkedin:send-post": "LinkedInPostInput",
};

export const ACTION_OUTPUTS_KEYED = {
  "harvest:add-historical-time-entry": "HarvestTimeEntry",
  "harvest:create-client": "HarvestClient",
  "harvest:create-project": "HarvestProject",
  "harvest:delete-project": "HarvestDeleteProjectOutput",
  "harvest:delete-time-entry": "HarvestDeleteTimeEntryOutput",
  "harvest:get-client": "HarvestClient",
  "harvest:get-project": "HarvestProject",
  "harvest:get-time-entry": "HarvestTimeEntry",
  "harvest:list-clients": "HarvestClientList",
  "harvest:list-projects": "HarvestProjectList",
  "harvest:list-project-tasks": "HarvestProjectTaskList",
  "harvest:list-tasks": "HarvestTaskList",
  "harvest:list-time-entries": "HarvestTimeEntryList",
  "harvest:restart-timer": "HarvestTimeEntry",
  "harvest:start-timer": "HarvestTimeEntry",
  "harvest:stop-timer": "HarvestTimeEntry",
  "harvest:update-time-entry": "HarvestTimeEntry",
  "github:add-pull-request-review-comment": "GithubPullRequestComment",
  "github:create-issue": "GithubIssue",
  "github:create-organization-repository": "GithubRepository",
  "github:create-pull-request": "GithubPullRequest",
  "github:create-pull-request-review": "GithubPullRequestReview",
  "github:create-repository": "GithubRepository",
  "github:delete-repository": "GithubDeleteRepositoryOutput",
  "github:get-issue": "GithubIssue",
  "github:get-pull-request": "GithubPullRequest",
  "github:get-pull-request-comments": "GithubPullRequestCommentList",
  "github:get-pull-request-files": "GithubPullRequestFileList",
  "github:get-pull-request-status": "GithubCombinedStatus",
  "github:get-repository": "GithubRepository",
  "github:list-branches": "GithubBranchList",
  "github:list-issues": "GithubIssueList",
  "github:list-pull-requests": "GithubPullRequestList",
  "github:list-repositories": "GithubRepositoryList",
  "github:merge-pull-request": "GithubMergeResult",
  "github:update-issue": "GithubIssue",
  "github:update-pull-request": "GithubPullRequest",
  "github:update-pull-request-branch": "GithubBranchUpdateResult",
  "github:update-repository": "GithubRepository",
  "github:write-file": "GithubWriteFileActionResult",
  "slack:add-reaction-as-user": "SlackReactionOutput",
  "slack:create-channel": "SlackCreateChannelOutput",
  "slack:delete-message-as-user": "SlackDeleteMessageOutput",
  "slack:get-channel-history": "SlackMessageList",
  "slack:get-message-permalink": "SlackPermalinkOutput",
  "slack:get-user-info": "SlackUserInfo",
  "slack:list-channels": "SlackConversationsList",
  "slack:search-messages": "SlackSearchResultList",
  "slack:send-message-as-user": "SlackSendMessageOutput",
  "slack:update-message-as-user": "SlackUpdateMessageOutput",
  "google-sheet:append-sheet": "GoogleSheetAppendOutput",
  "google-sheet:create-sheet": "GoogleSheetCreateOutput",
  "google-sheet:edit-sheet": "GoogleSheetUpdateOutput",
  "google-calendar:create-event": "GoogleCalendarEvent",
  "google-calendar:delete-event": "GoogleCalendarEventDeleteOutput",
  "google-calendar:list-calendars": "GoogleCalendarList",
  "google-calendar:list-events": "GoogleCalendarEventList",
  "google-calendar:update-event": "GoogleCalendarEvent",
  "google-mail:compose-draft": "GmailDraftOutput",
  "google-mail:compose-draft-reply": "GmailReplyDraftOutput",
  "google-mail:create-filter": "GmailFilter",
  "google-mail:delete-message": "GmailDeleteMessageOutput",
  "google-mail:get-message": "GmailMessage",
  "google-mail:list-messages": "GmailMessageList",
  "google-mail:modify-message-labels": "GmailMessage",
  "google-mail:send-email": "GmailSendEmailOutput",
  "google-mail:trash-message": "GmailMessage",
  "google-mail:untrash-message": "GmailMessage",
  "dropbox:copy-file": "DropboxEntry",
  "dropbox:create-folder": "DropboxFolder",
  "dropbox:delete-file": "DropboxDeleteResult",
  "dropbox:get-file": "DropboxFile",
  "dropbox:list-files": "DropboxFileList",
  "dropbox:move-file": "DropboxEntry",
  "dropbox:search-files": "DropboxSearchResult",
  "dropbox:upload-file": "DropboxFile",
  "notion:create-database": "NotionDatabase",
  "notion:create-page": "NotionPageOrDatabase",
  "notion:get-database": "NotionDatabase",
  "notion:get-page": "NotionPageOrDatabase",
  "notion:query-database": "NotionQueryDatabaseOutput",
  "notion:search": "NotionSearchOutput",
  "notion:update-database": "NotionDatabase",
  "notion:update-page": "NotionPageOrDatabase",
  "google-docs:create-document": "GoogleDocsDocument",
  "google-docs:get-document": "GoogleDocsDocument",
  "google-docs:update-document": "GoogleDocsUpdateDocumentOutput",
  "linear:create-issue": "LinearIssue",
  "linear:create-project": "LinearProject",
  "linear:delete-issue": "LinearDeleteIssueOutput",
  "linear:fetch-models": "ModelResponse",
  "linear:get-issue": "LinearIssue",
  "linear:get-project": "LinearProject",
  "linear:get-team": "LinearTeam",
  "linear:list-issues": "LinearIssueList",
  "linear:list-projects": "LinearProjectList",
  "linear:list-teams": "LinearTeamList",
  "linear:update-issue": "LinearIssue",
  "linear:update-project": "LinearProject",
  "google-drive:list-documents": "GoogleDriveDocumentList",
  "google-drive:list-root-folders": "GoogleDriveFolderList",
  "twitter-v2:get-user-profile": "XSocialUserProfile",
  "twitter-v2:send-post": "XSocialPostOutput",
  "linkedin:get-user-profile": "LinkedInUserProfile",
  "linkedin:send-post": "LinkedInPostOutput",
};

export const SYNC_OUTPUTS_KEYED = {
  "slack:messages": "SlackSyncMessage",
  "google-calendar:events-fork": "GoogleCalendarEvent",
  "google-mail:emails-fork": "GmailEmail",
  "dropbox:files-fork": "DropboxFile",
  "google-drive:documents-fork": "Document",
};

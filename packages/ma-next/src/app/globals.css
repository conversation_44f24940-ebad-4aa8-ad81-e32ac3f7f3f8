@import 'tailwindcss'; /* Replaced v3 directives with single v4 import */

/* Custom scrollbar styles from makeagent/src/index.css */
@layer utilities {
  .scrollbar-themed {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.gray.300') transparent;
  }

  .scrollbar-themed::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-themed::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px;
  }

  .scrollbar-themed::-webkit-scrollbar-thumb {
    background-color: theme('colors.gray.300');
    border-radius: 32px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  .dark .scrollbar-themed {
    scrollbar-color: theme('colors.gray.600') transparent;
  }

  .dark .scrollbar-themed::-webkit-scrollbar-thumb {
    background-color: theme('colors.gray.600');
  }

  /* Hide scrollbar when not needed */
  .auto-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .auto-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .auto-scrollbar.overflow::-webkit-scrollbar {
    display: block;
  }

  /* REMOVED: Global application to html via @apply */
  /* html {
    @apply scrollbar-themed;
  } */
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Markdown styles from makeagent/src/index.css */
.markdown h1 {
  font-size: 2em;
  margin-bottom: 0.5em;
}

.markdown h2 {
  font-size: 1.5em;
  margin-bottom: 0.5em;
}

.markdown h3 {
  font-size: 1.25em;
  margin-bottom: 0.5em;
}

.markdown p {
  margin-bottom: 1em;
}

.markdown strong {
  font-weight: 600;
}

.markdown em {
  font-style: italic;
}

.markdown ul {
  list-style: disc;
  margin-bottom: 1.3em;
  margin-left: 32px;
}

.markdown ul li {
  margin-bottom: 0.5em;
}

.markdown ol {
  list-style: decimal;
  margin-bottom: 1.3em;
  margin-left: 32px;
}

.markdown ol li {
  margin-bottom: 0.5em;
}

.markdown code {
  font-family:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New',
    monospace;
  font-size: 0.9em;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  background-color: rgba(0, 0, 0, 0.05);
}

.dark .markdown code {
  background-color: rgba(255, 255, 255, 0.1);
}

.markdown pre {
  margin-bottom: 1.3em;
  padding: 1em;
  border-radius: 0.5em;
  background-color: rgba(0, 0, 0, 0.05);
  overflow-x: auto;
}

.dark .markdown pre {
  background-color: rgba(255, 255, 255, 0.05);
}

.markdown pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

.markdown blockquote {
  border-left: 4px solid rgba(0, 0, 0, 0.1);
  padding-left: 1em;
  margin-left: 0;
  margin-bottom: 1.3em;
  font-style: italic;
}

.dark .markdown blockquote {
  border-left-color: rgba(255, 255, 255, 0.2);
}

.markdown a {
  color: #3b82f6;
  text-decoration: underline;
}

.markdown a:hover {
  text-decoration: none;
}

.dark .markdown a {
  color: #60a5fa;
}

button {
  cursor: pointer;
}

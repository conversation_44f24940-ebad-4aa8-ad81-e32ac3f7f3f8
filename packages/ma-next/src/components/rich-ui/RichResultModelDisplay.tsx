import { GmailMessageDisplay } from './rich-results/GmailMessageDisplay';
import { GmailSingleMessageDisplay } from './rich-results/GmailSingleMessageDisplay';
import { GmailDraftOutputDisplay } from './rich-results/GmailDraftOutputDisplay';
import { GmailSendEmailOutputDisplay } from './rich-results/GmailSendEmailOutputDisplay';
import { GmailEmailDisplay } from './rich-results/GmailEmailDisplay';
import { GoogleCalendarEventListDisplay } from './rich-results/GoogleCalendarEventListDisplay';
import { GoogleCalendarEventDisplay } from './rich-results/GoogleCalendarEventDisplay';
import { GoogleCalendarEventDeleteOutputDisplay } from './rich-results/GoogleCalendarEventDeleteOutputDisplay';
import { GoogleCalendarListDisplay } from './rich-results/GoogleCalendarListDisplay';
import { GoogleDocsCreateDocumentDisplay } from './rich-results/GoogleDocsCreateDocumentDisplay';
import { GoogleDocsFetchDocumentDisplay } from './rich-results/GoogleDocsFetchDocumentDisplay';
import { GoogleDocsUpdateDocumentDisplay } from './rich-results/GoogleDocsUpdateDocumentDisplay';
import { GoogleSheetCreateSheetDisplay } from './rich-results/GoogleSheetCreateSheetDisplay';
import { GoogleSheetFetchSpreadsheetDisplay } from './rich-results/GoogleSheetFetchSpreadsheetDisplay';
import { GoogleSheetUpdateSheetDisplay } from './rich-results/GoogleSheetUpdateSheetDisplay';
import { GoogleSheetAppendOutputDisplay } from './rich-results/GoogleSheetAppendOutputDisplay';
import { GoogleDriveDocumentListDisplay } from './rich-results/GoogleDriveDocumentListDisplay';
import { GoogleDriveFolderListDisplay } from './rich-results/GoogleDriveFolderListDisplay';
import { FolderContentDisplay } from './rich-results/FolderContentDisplay';
import { GoogleDriveFetchDocumentDisplay } from './rich-results/GoogleDriveFetchDocumentDisplay';
import { GoogleDriveFetchGoogleDocDisplay } from './rich-results/GoogleDriveFetchGoogleDocDisplay';
import { GoogleDriveFetchGoogleSheetDisplay } from './rich-results/GoogleDriveFetchGoogleSheetDisplay';
import { GoogleDriveUploadDocumentDisplay } from './rich-results/GoogleDriveUploadDocumentDisplay';
import { SlackConversationListDisplay } from './rich-results/SlackConversationListDisplay';
import { SlackMessageListDisplay } from './rich-results/SlackMessageListDisplay';
import { SlackSendMessageResultDisplay } from './rich-results/SlackSendMessageResultDisplay';
import { SlackUserInfoDisplay } from './rich-results/SlackUserInfoDisplay';
import { SlackUpdateMessageOutputDisplay } from './rich-results/SlackUpdateMessageOutputDisplay';
import { SlackSyncMessageDisplay } from './rich-results/SlackSyncMessageDisplay';
import { SlackActionResultDisplay } from './rich-results/SlackActionResultDisplay';
import { SlackSearchResultListDisplay } from './rich-results/SlackSearchResultListDisplay';
import { DropboxFolderDisplay } from './rich-results/DropboxFolderDisplay';
import { DropboxFileDisplay } from './rich-results/DropboxFileDisplay';
import { DropboxFileListDisplay } from './rich-results/DropboxFileListDisplay';
import { DropboxSearchResultDisplay } from './rich-results/DropboxSearchResultDisplay';
import { NotionPageOrDatabaseDisplay } from './rich-results/NotionPageOrDatabaseDisplay';
import { NotionSearchResultDisplay } from './rich-results/NotionSearchResultDisplay';
import { XSocialUserProfileDisplay } from './rich-results/XSocialUserProfileDisplay';
import { XSocialPostOutputDisplay } from './rich-results/XSocialPostOutputDisplay';
import { LinkedInUserProfileDisplay } from './rich-results/LinkedInUserProfileDisplay';
import { GithubRepositoryDisplay } from './rich-results/GithubRepositoryDisplay';
import { GithubRepositoryListDisplay } from './rich-results/GithubRepositoryListDisplay';
import { GithubIssueDisplay } from './rich-results/GithubIssueDisplay';
import { GithubIssueListDisplay } from './rich-results/GithubIssueListDisplay';
import { GithubPullRequestDisplay } from './rich-results/GithubPullRequestDisplay';
import { GithubPullRequestListDisplay } from './rich-results/GithubPullRequestListDisplay';
import { GithubBranchListDisplay } from './rich-results/GithubBranchListDisplay';
import { GithubCombinedStatusDisplay } from './rich-results/GithubCombinedStatusDisplay';
import { GithubMergeResultDisplay } from './rich-results/GithubMergeResultDisplay';
import { GithubBranchUpdateResultDisplay } from './rich-results/GithubBranchUpdateResultDisplay';
import { GithubWriteFileActionResultDisplay } from './rich-results/GithubWriteFileActionResultDisplay';
import { GithubPullRequestCommentListDisplay } from './rich-results/GithubPullRequestCommentListDisplay';
import { GithubPullRequestFileListDisplay } from './rich-results/GithubPullRequestFileListDisplay';
import { GithubPullRequestCommentDisplay } from './rich-results/GithubPullRequestCommentDisplay';
import { GithubDeleteRepositoryOutputDisplay } from './rich-results/GithubDeleteRepositoryOutputDisplay';
import { GithubRepoDisplay } from './rich-results/GithubRepoDisplay';
import { GithubPullRequestReviewDisplay } from './rich-results/GithubPullRequestReviewDisplay';
import { LinearIssueDisplay } from './rich-results/LinearIssueDisplay';
import { LinearIssueListDisplay } from './rich-results/LinearIssueListDisplay';
import { LinearProjectDisplay } from './rich-results/LinearProjectDisplay';
import { LinearProjectListDisplay } from './rich-results/LinearProjectListDisplay';
import { LinearTeamDisplay } from './rich-results/LinearTeamDisplay';
import { LinearTeamListDisplay } from './rich-results/LinearTeamListDisplay';
import { HarvestTimeEntryDisplay } from './rich-results/HarvestTimeEntryDisplay';
import { HarvestClientDisplay } from './rich-results/HarvestClientDisplay';
import { HarvestProjectDisplay } from './rich-results/HarvestProjectDisplay';
import { HarvestTimeEntryListDisplay } from './rich-results/HarvestTimeEntryListDisplay';
import { HarvestDeleteProjectDisplay } from './rich-results/HarvestDeleteProjectDisplay';
import { HarvestDeleteTimeEntryDisplay } from './rich-results/HarvestDeleteTimeEntryDisplay';
import { HarvestClientListDisplay } from './rich-results/HarvestClientListDisplay';
import { HarvestProjectListDisplay } from './rich-results/HarvestProjectListDisplay';
import { HarvestProjectTaskListDisplay } from './rich-results/HarvestProjectTaskListDisplay';
import { HarvestTaskListDisplay } from './rich-results/HarvestTaskListDisplay';

const RICH_RESULT_MODEL_COMPONENTS: Record<
  string,
  React.FC<{ output: any; actionParameters?: Record<string, any>; context?: string }>
> = {
  GmailMessageList: GmailMessageDisplay,
  GmailMessage: GmailSingleMessageDisplay,
  GmailEmail: GmailEmailDisplay,
  GmailDraftOutput: GmailDraftOutputDisplay,
  GmailSendEmailOutput: GmailSendEmailOutputDisplay,
  GoogleCalendarEventList: GoogleCalendarEventListDisplay,
  GoogleCalendarEvent: GoogleCalendarEventDisplay,
  GoogleCalendarEventDeleteOutput: GoogleCalendarEventDeleteOutputDisplay,
  GoogleCalendarList: GoogleCalendarListDisplay,
  GoogleDocsDocument: GoogleDocsCreateDocumentDisplay,
  Document: GoogleDocsFetchDocumentDisplay,
  GoogleDocsUpdateDocumentOutput: GoogleDocsUpdateDocumentDisplay,
  GoogleSheetCreateOutput: GoogleSheetCreateSheetDisplay,
  Spreadsheet: GoogleSheetFetchSpreadsheetDisplay,
  GoogleSheetUpdateOutput: GoogleSheetUpdateSheetDisplay,
  GoogleSheetAppendOutput: GoogleSheetAppendOutputDisplay,
  GoogleDriveDocumentList: GoogleDriveDocumentListDisplay,
  GoogleDriveFolderList: GoogleDriveFolderListDisplay,
  FolderContent: FolderContentDisplay,
  Anonymous_googledrive_action_fetchdocument_output: GoogleDriveFetchDocumentDisplay,
  JSONDocument: GoogleDriveFetchGoogleDocDisplay,
  JSONSpreadsheet: GoogleDriveFetchGoogleSheetDisplay,
  GoogleDocument: GoogleDriveUploadDocumentDisplay,
  SlackConversationsList: SlackConversationListDisplay,
  SlackMessageList: SlackMessageListDisplay,
  SlackSendMessageOutput: SlackSendMessageResultDisplay,
  SlackUserInfo: SlackUserInfoDisplay,
  SlackUpdateMessageOutput: SlackUpdateMessageOutputDisplay,
  SlackSyncMessage: SlackSyncMessageDisplay,
  SlackReactionOutput: (props: any) => <SlackActionResultDisplay {...props} actionType="reaction" />,
  SlackDeleteMessageOutput: (props: any) => <SlackActionResultDisplay {...props} actionType="delete" />,
  SlackPermalinkOutput: (props: any) => <SlackActionResultDisplay {...props} actionType="permalink" />,
  SlackSearchResultList: SlackSearchResultListDisplay,
  DropboxFolder: DropboxFolderDisplay,
  DropboxFile: DropboxFileDisplay,
  DropboxFileList: DropboxFileListDisplay,
  DropboxSearchResult: DropboxSearchResultDisplay,
  NotionPageOrDatabase: NotionPageOrDatabaseDisplay,
  NotionSearchOutput: NotionSearchResultDisplay,
  XSocialUserProfile: XSocialUserProfileDisplay,
  XSocialPostOutput: XSocialPostOutputDisplay,
  LinkedInUserProfile: LinkedInUserProfileDisplay,
  GithubRepository: GithubRepositoryDisplay,
  GithubRepositoryList: GithubRepositoryListDisplay,
  GithubIssue: GithubIssueDisplay,
  GithubIssueList: GithubIssueListDisplay,
  GithubPullRequest: GithubPullRequestDisplay,
  GithubPullRequestList: GithubPullRequestListDisplay,
  GithubBranchList: GithubBranchListDisplay,
  GithubCombinedStatus: GithubCombinedStatusDisplay,
  GithubMergeResult: GithubMergeResultDisplay,
  GithubBranchUpdateResult: GithubBranchUpdateResultDisplay,
  GithubWriteFileActionResult: GithubWriteFileActionResultDisplay,
  GithubPullRequestCommentList: GithubPullRequestCommentListDisplay,
  GithubPullRequestFileList: GithubPullRequestFileListDisplay,
  GithubPullRequestComment: GithubPullRequestCommentDisplay,
  GithubDeleteRepositoryOutput: GithubDeleteRepositoryOutputDisplay,
  GithubRepo: GithubRepoDisplay,
  GithubPullRequestReview: GithubPullRequestReviewDisplay,
  LinearIssue: LinearIssueDisplay,
  LinearIssueList: LinearIssueListDisplay,
  LinearProject: LinearProjectDisplay,
  LinearProjectList: LinearProjectListDisplay,
  LinearTeam: LinearTeamDisplay,
  LinearTeamList: LinearTeamListDisplay,
  HarvestTimeEntry: HarvestTimeEntryDisplay,
  HarvestClient: HarvestClientDisplay,
  HarvestProject: HarvestProjectDisplay,
  HarvestTimeEntryList: HarvestTimeEntryListDisplay,
  HarvestDeleteProjectOutput: HarvestDeleteProjectDisplay,
  HarvestDeleteTimeEntryOutput: HarvestDeleteTimeEntryDisplay,
  HarvestClientList: HarvestClientListDisplay,
  HarvestProjectList: HarvestProjectListDisplay,
  HarvestProjectTaskList: HarvestProjectTaskListDisplay,
  HarvestTaskList: HarvestTaskListDisplay,
};

interface RichResultModelDisplayProps {
  modelName: string;
  result?: any;
  actionParameters?: Record<string, any>;
  context?: string;
}

function RichResultModelDisplay({
  modelName,
  result,
  actionParameters,
  context,
}: RichResultModelDisplayProps) {
  const DisplayComponent = RICH_RESULT_MODEL_COMPONENTS[modelName];
  if (!DisplayComponent) return null;
  return (
    <div className="mb-8 rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
      <DisplayComponent output={result} actionParameters={actionParameters} context={context} />
    </div>
  );
}

RichResultModelDisplay.canDisplay = (modelName: string) => {
  return Boolean(RICH_RESULT_MODEL_COMPONENTS[modelName]);
};

export { RichResultModelDisplay };

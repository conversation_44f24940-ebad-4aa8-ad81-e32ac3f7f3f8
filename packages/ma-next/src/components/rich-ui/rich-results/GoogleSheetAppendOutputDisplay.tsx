import React from 'react';
import { Database, MoreHorizontal, ExternalLink, Check } from 'lucide-react';
import { GoogleSheetAppendOutput } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GoogleSheetAppendOutputDisplayProps = {
  output: GoogleSheetAppendOutput;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Google Sheet append result
 */
function GoogleSheetAppendOutputDisplay({
  output,
  actionParameters
}: GoogleSheetAppendOutputDisplayProps) {
  if (!output || !output.spreadsheetId) {
    return (
      <div className="p-6 text-center">
        <Database className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No append information available</p>
      </div>
    );
  }

  // Extract spreadsheet name from action parameters if available
  const spreadsheetName = actionParameters?.spreadsheetName || 'Spreadsheet Updated';

  // Get the appended data from the updates
  const appendedData = output.updates?.updatedData?.values || [];
  const updatedRows = output.updates?.updatedRows || 0;

  const sheetUrl = `https://docs.google.com/spreadsheets/d/${output.spreadsheetId}/edit`;
  const menuItems: ContextMenuItem[] = [
    {
      label: 'Open in Google Sheets',
      href: sheetUrl,
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Check className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Google Sheet Updated
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

        <div className="p-5">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Database className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {spreadsheetName}
              </h4>

              {updatedRows > 0 && (
                <div className="mt-3">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {updatedRows} row{updatedRows !== 1 ? 's' : ''} added
                  </div>

                  {appendedData.length > 0 && (
                    <div className="bg-gray-50 dark:bg-gray-800 rounded-md p-3 overflow-hidden">
                        <div className="overflow-x-auto no-scrollbar">
                        <table className="min-w-full text-xs">
                          <tbody className="space-y-1">
                            {appendedData.slice(0, 3).map((row, rowIndex) => (
                              <tr key={rowIndex} className="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                                {row.slice(0, 4).map((cell, cellIndex) => (
                                  <td
                                    key={cellIndex}
                                    className="px-2 py-1 text-gray-700 dark:text-gray-300 border-r border-gray-200 dark:border-gray-700 last:border-r-0"
                                  >
                                    <div className="truncate max-w-[100px]" title={cell}>
                                      {cell || '—'}
                                    </div>
                                  </td>
                                ))}
                                {row.length > 4 && (
                                  <td className="px-2 py-1 text-gray-500 dark:text-gray-400">
                                    ...
                                  </td>
                                )}
                              </tr>
                            ))}
                            {appendedData.length > 3 && (
                              <tr>
                                <td colSpan={Math.min(appendedData[0]?.length || 1, 5)} className="px-2 py-1 text-center text-gray-500 dark:text-gray-400">
                                  ... and {appendedData.length - 3} more row{appendedData.length - 3 !== 1 ? 's' : ''}
                                </td>
                              </tr>
                            )}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ContextMenu>
  );
}

export { GoogleSheetAppendOutputDisplay };

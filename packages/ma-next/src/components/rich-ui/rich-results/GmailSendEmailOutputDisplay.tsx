import React from 'react';
import { Mail, Send, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GmailSendEmailOutput, UrlAccessibleFile } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';
import { GmailAttachments } from './shared/GmailAttachments';

type GmailSendEmailOutputDisplayProps = {
  output: GmailSendEmailOutput;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Gmail send email result
 */
function GmailSendEmailOutputDisplay({
  output,
  actionParameters,
}: GmailSendEmailOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No email data available</p>
      </div>
    );
  }

  // Extract email info from action parameters
  const to = actionParameters?.to;
  const subject = actionParameters?.subject;
  const attachments = actionParameters?.attachments as UrlAccessibleFile[] | undefined;

  const menuItems: ContextMenuItem[] = [
    {
      label: 'View in Gmail Sent Folder',
      href: 'https://mail.google.com/mail/u/0/#sent',
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Send className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Gmail Email Sent
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

        <div className="p-5">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Mail className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {subject || 'Email Sent Successfully'}
              </h4>

              {to && (
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center mb-1">
                    <span className="font-medium mr-2">To:</span>
                    <span>{to}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Attachments */}
          {attachments && attachments.length > 0 && (
            <div className="mt-4">
              <GmailAttachments attachments={attachments} />
            </div>
          )}
        </div>
      </div>
    </ContextMenu>
  );
}


export { GmailSendEmailOutputDisplay };

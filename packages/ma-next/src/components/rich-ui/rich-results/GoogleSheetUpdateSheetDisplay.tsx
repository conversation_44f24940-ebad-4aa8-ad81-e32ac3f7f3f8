import React from 'react';
import { Database, Check, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GoogleSheetUpdateOutput } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GoogleSheetUpdateSheetDisplayProps = {
  output: GoogleSheetUpdateOutput;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a Google Sheet update result
 */
function GoogleSheetUpdateSheetDisplay({
  output,
  actionParameters
}: GoogleSheetUpdateSheetDisplayProps) {
  if (!output || !output.spreadsheetId) {
    return (
      <div className="p-6 text-center">
        <Database className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No update information available</p>
      </div>
    );
  }

  // Extract spreadsheet name from action parameters if available
  const spreadsheetName = actionParameters?.spreadsheetName || 'Spreadsheet Updated';

  const sheetUrl = `https://docs.google.com/spreadsheets/d/${output.spreadsheetId}/edit`;
  const menuItems: ContextMenuItem[] = [
    {
      label: 'Open in Google Sheets',
      href: sheetUrl,
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Check className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Google Sheet Updated
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

        <div className="p-5">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
                <Database className="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {spreadsheetName}
              </h4>

              <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center mb-1">
                  <span className="font-medium mr-2">Updated Range:</span>
                  <span>{output.updatedRange}</span>
                </div>
              </div>

              <div className="mt-3 grid grid-cols-3 gap-4">
                <div className="bg-green-50 dark:bg-green-900/30 rounded-md p-3 text-center">
                  <div className="text-xl font-semibold text-green-600 dark:text-green-400">
                    {output.updatedRows}
                  </div>
                  <div className="text-xs text-green-800 dark:text-green-300">Rows Updated</div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/30 rounded-md p-3 text-center">
                  <div className="text-xl font-semibold text-green-600 dark:text-green-400">
                    {output.updatedColumns}
                  </div>
                  <div className="text-xs text-green-800 dark:text-green-300">Columns Updated</div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/30 rounded-md p-3 text-center">
                  <div className="text-xl font-semibold text-green-600 dark:text-green-400">
                    {output.updatedCells}
                  </div>
                  <div className="text-xs text-green-800 dark:text-green-300">Cells Updated</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ContextMenu>
  );
}

export { GoogleSheetUpdateSheetDisplay };

import React from 'react';
import { Database, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GoogleSheetCreateOutput } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GoogleSheetCreateSheetDisplayProps = {
  output: GoogleSheetCreateOutput;
};

/**
 * Renders a rich display of a newly created Google Sheet
 */
function GoogleSheetCreateSheetDisplay({ output }: GoogleSheetCreateSheetDisplayProps) {
  if (!output || !output.id) {
    return (
      <div className="p-6 text-center">
        <Database className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No spreadsheet information available</p>
      </div>
    );
  }

  const sheetUrl = output.url || `https://docs.google.com/spreadsheets/d/${output.id}/edit`;
  const menuItems: ContextMenuItem[] = [
    {
      label: 'Open in Google Sheets',
      href: sheetUrl,
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Database className="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Google Sheet Created
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <Database className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {output.title || 'Untitled Spreadsheet'}
            </h4>

            <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Sheet created
            </div>

            <div className="mt-4" />
          </div>
        </div>
      </div>
    </div>
    </ContextMenu>
  );
}

export { GoogleSheetCreateSheetDisplay };

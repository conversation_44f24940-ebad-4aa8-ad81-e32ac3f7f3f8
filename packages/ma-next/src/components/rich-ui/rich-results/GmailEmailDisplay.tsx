import React, { useState } from 'react';
import { ChevronDown, ChevronRight, Mail } from 'lucide-react';
import { GmailEmail } from 'src/config/nangoModels';
import { GmailSingleMessageDisplay } from './GmailSingleMessageDisplay';
import { gmailEmailToMessage } from './shared/gmail';

type GmailEmailDisplayProps = {
  output: GmailEmail;
};

/**
 * Display a GmailEmail with collapsible functionality for the iframe content.
 */
function GmailEmailDisplay({ output }: GmailEmailDisplayProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const message = gmailEmailToMessage(output);

  // Extract basic info for the collapsed header
  const headers = message.headers || [];
  const getHeader = (name: string): string => {
    const header = headers.find(h => h.name.toLowerCase() === name.toLowerCase());
    return header ? header.value : '';
  };

  const subject = getHeader('subject') || 'No Subject';
  const from = getHeader('from') || '';

  // If expanded, show the full component
  if (isExpanded) {
    return (
      <div className="relative">
        <GmailSingleMessageDisplay output={message} />
        {/* Collapse button overlay */}
        <button
          onClick={() => setIsExpanded(false)}
          className="absolute top-4 right-4 p-1 rounded-md bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
          title="Collapse email"
        >
          <ChevronDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
        </button>
      </div>
    );
  }

  // Collapsed state - show just a bar with chevron and basic info
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-white dark:bg-gray-800">
      {/* Collapsed header bar */}
      <button
        onClick={() => setIsExpanded(true)}
        className="w-full px-4 py-3 flex items-center justify-between hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      >
        <div className="flex items-center min-w-0 flex-1">
          <Mail className="w-4 h-4 text-blue-600 dark:text-blue-400 mr-3 flex-shrink-0" />
          <div className="min-w-0 flex-1 text-left">
            <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
              {subject}
            </div>
            {from && (
              <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                From: {from}
              </div>
            )}
          </div>
        </div>
        <ChevronRight className="w-4 h-4 text-gray-500 dark:text-gray-400 flex-shrink-0 ml-2" />
      </button>

      {/* Small sliver of the content */}
      <div className="h-10 overflow-hidden border-t border-gray-200 dark:border-gray-700">
        <div className="transform scale-75 origin-top-left w-[133%] pointer-events-none">
          <GmailSingleMessageDisplay output={message} />
        </div>
      </div>
    </div>
  );
}

export { GmailEmailDisplay };

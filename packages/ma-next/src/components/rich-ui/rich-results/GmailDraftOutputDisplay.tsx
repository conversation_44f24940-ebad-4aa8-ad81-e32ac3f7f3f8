import React from 'react';
import { Mail, FileText, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GmailDraftOutput, UrlAccessibleFile } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';
import { GmailAttachments } from './shared/GmailAttachments';

type GmailDraftOutputDisplayProps = {
  output: GmailDraftOutput;
  actionParameters?: Record<string, any>;
};


/**
 * Renders a rich display of a Gmail draft creation result
 */
function GmailDraftOutputDisplay({ output, actionParameters }: GmailDraftOutputDisplayProps) {
  if (!output) {
    return (
      <div className="p-6 text-center">
        <Mail className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No draft data available</p>
      </div>
    );
  }

  // Extract draft info from action parameters
  const recipient = actionParameters?.recipient;
  const subject = actionParameters?.subject;
  const attachments = actionParameters?.attachments as UrlAccessibleFile[] | undefined;

  const menuItems: ContextMenuItem[] = [
    {
      label: 'View in Gmail Drafts',
      href: 'https://mail.google.com/mail/u/0/#drafts',
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Gmail Draft Created
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

        <div className="p-5">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <Mail className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {subject || 'Draft Created'}
              </h4>

              {recipient && (
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center mb-1">
                    <span className="font-medium mr-2">To:</span>
                    <span>{recipient}</span>
                  </div>
                </div>
              )}

              {/* Body preview if available */}
              {actionParameters?.body && (
                <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-900/30 rounded border border-gray-200 dark:border-gray-700">
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Body Preview
                  </div>
                  <div className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line line-clamp-3">
                    {actionParameters.body}
                  </div>
                </div>
              )}

              {/* Attachments */}
              {attachments && attachments.length > 0 && (
                <div className="mt-4">
                  <GmailAttachments attachments={attachments} />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </ContextMenu>
  );
}

export { GmailDraftOutputDisplay };

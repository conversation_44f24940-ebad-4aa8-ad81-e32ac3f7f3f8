import React from 'react';
import { FileText, MoreHorizontal, ExternalLink } from 'lucide-react';
import { format } from 'date-fns';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

// Since we don't have a clear definition for the JSONDocument model, we'll create a flexible interface
interface JSONDocument {
  id?: string;
  name?: string;
  mimeType?: string;
  content?: string;
  body?: any;
  modifiedTime?: string;
  createdTime?: string;
  webViewLink?: string;
  [key: string]: any; // Allow for any other properties
}

type GoogleDriveFetchGoogleDocDisplayProps = {
  output: JSONDocument;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a fetched Google Doc in JSON format
 */
function GoogleDriveFetchGoogleDocDisplay({
  output,
  actionParameters: _actionParameters,
}: GoogleDriveFetchGoogleDocDisplayProps) {
  if (!output || (!output.id && !output.name)) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No document information available</p>
      </div>
    );
  }

  // Format dates if available
  let formattedModifiedDate = '';
  let formattedCreatedDate = '';

  try {
    if (output.modifiedTime) {
      formattedModifiedDate = format(new Date(output.modifiedTime), 'MMM d, yyyy h:mm a');
    }
    if (output.createdTime) {
      formattedCreatedDate = format(new Date(output.createdTime), 'MMM d, yyyy h:mm a');
    }
  } catch (e) {
    // Use raw date strings if formatting fails
    formattedModifiedDate = output.modifiedTime || '';
    formattedCreatedDate = output.createdTime || '';
  }

  // Determine if we have content to display
  const hasContent = output.content || (output.body && typeof output.body === 'string');
  const content = output.content || (typeof output.body === 'string' ? output.body : null);

  const menuItems: ContextMenuItem[] = [];
  if (output.webViewLink) {
    menuItems.push({
      label: 'Open in Google Docs',
      href: output.webViewLink,
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    });
  }

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">Google Doc</h3>
            </div>
            {menuItems.length > 0 && (
              <DropdownMenu
                trigger={
                  <button
                    className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                    title="More options"
                  >
                    <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  </button>
                }
                items={menuItems}
              />
            )}
          </div>
        </div>

        <div className="p-5">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                {output.name || 'Untitled Document'}
              </h4>

              {(formattedModifiedDate || formattedCreatedDate) && (
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  {formattedModifiedDate && (
                    <div className="flex items-center mb-1">
                      <span className="font-medium mr-2">Modified:</span>
                      <span>{formattedModifiedDate}</span>
                    </div>
                  )}

                  {formattedCreatedDate && (
                    <div className="flex items-center mb-1">
                      <span className="font-medium mr-2">Created:</span>
                      <span>{formattedCreatedDate}</span>
                    </div>
                  )}
                </div>
              )}

              {hasContent && (
                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                    Document Content Preview:
                  </h5>
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <div
                      className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line max-h-60 overflow-y-auto"
                      dangerouslySetInnerHTML={{ __html: content || '' }}
                    />
                  </div>
                </div>
              )}


            </div>
          </div>
        </div>
      </div>
    </ContextMenu>
  );
}

export { GoogleDriveFetchGoogleDocDisplay };

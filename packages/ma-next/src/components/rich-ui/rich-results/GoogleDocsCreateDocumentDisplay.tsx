import React from 'react';
import { FileText, Link, Calendar, MoreHorizontal, ExternalLink } from 'lucide-react';
import { GoogleDocsDocument } from 'src/config/nangoModels';
import { ContextMenu, ContextMenuItem } from '../common/ContextMenu';
import { DropdownMenu } from '../common/DropdownMenu';

type GoogleDocsCreateDocumentDisplayProps = {
  output: GoogleDocsDocument;
  actionParameters?: Record<string, any>;
};

/**
 * Renders a rich display of a newly created Google Docs document
 */
function GoogleDocsCreateDocumentDisplay({
  output,
  actionParameters
}: GoogleDocsCreateDocumentDisplayProps) {
  if (!output || !output.documentId) {
    return (
      <div className="p-6 text-center">
        <FileText className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No document information available</p>
      </div>
    );
  }

  const docUrl = `https://docs.google.com/document/d/${output.documentId}/edit`;
  const menuItems: ContextMenuItem[] = [
    {
      label: 'Open in Google Docs',
      href: docUrl,
      icon: <ExternalLink className="w-4 h-4 mr-2" />,
    },
  ];

  return (
    <ContextMenu items={menuItems}>
      <div>
        <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2" />
              <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                Google Docs Document Created
              </h3>
            </div>
            <DropdownMenu
              trigger={
                <button
                  className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  title="More options"
                >
                  <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                </button>
              }
              items={menuItems}
            />
          </div>
        </div>

      <div className="p-5">
        <div className="flex items-start">
          <div className="flex-shrink-0 mr-3">
            <div className="w-10 h-10 rounded-md bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <FileText className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
          <div className="min-w-0 flex-1">
            <h4 className="text-lg font-medium text-gray-900 dark:text-white">
              {output.title || 'Untitled Document'}
            </h4>

            <div className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Document created
            </div>
          </div>
        </div>
      </div>
    </div>
    </ContextMenu>
  );
}

export { GoogleDocsCreateDocumentDisplay };

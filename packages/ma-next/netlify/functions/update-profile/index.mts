import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { maybeEncrypt } from '../_shared/crypto';

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'POST') {
    return validationError('Method not allowed');
  }

  const [authError, user] = await initAuthenticate(req);
  if (authError) return authError;

  const body = await req.json();
  const updates: Record<string, any> = {};

  if (typeof body.firstName === 'string') {
    updates.firstName = await maybeEncrypt(body.firstName);
  }
  if (typeof body.lastName === 'string') {
    updates.lastName = await maybeEncrypt(body.lastName);
  }
  if (body.preferences !== undefined) {
    updates.preferences = body.preferences;
  }

  if (!Object.keys(updates).length) {
    return validationError('No valid fields provided');
  }

  const supabase = initServiceRoleSupabase();
  const { error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', user.id);

  if (error) return errorResponse(error);

  return new Response(JSON.stringify({ success: true }), { headers: corsHeaders });
}

import { SupabaseClient } from '@supabase/supabase-js';
import { maybeEncrypt, maybeDecrypt } from '../_shared/crypto';

export class TaskflowSupabaseFacade {
  private client: SupabaseClient;
  constructor(client: SupabaseClient) {
    this.client = client;
  }

  private async maybeEncrypt(data: any) {
    return maybeEncrypt(data);
  }

  private async maybeDecrypt(data: any) {
    return maybeDecrypt(data);
  }

  getProfile(userId: string) {
    return this.client
      .from('profiles')
      .select('id, firstName, lastName, preferences')
      .eq('id', userId)
      .single();
  }

  getActiveExecution(executionId: string) {
    return this.client
      .from('taskflow_executions')
      .select('id, context, triggerData, taskflowId')
      .eq('id', executionId)
      .is('completedAt', null)
      .maybeSingle()
      .then(async res => {
        if (res.data) {
          res.data.context = await this.maybeDecrypt(res.data.context);
          res.data.triggerData = await this.maybeDecrypt(res.data.triggerData);
        }
        return res;
      });
  }

  getUserTaskflow(taskflowId: string, userId: string) {
    return this.client
      .from('taskflows')
      .select(
        `
        taskflowSchema:schema,
        conversations!taskflows_conversationId_fkey!inner(
          id,
          userId
        )
      `
      )
      .eq('id', taskflowId)
      .eq('conversations.userId', userId)
      .single();
  }

  createExecution(taskflowId: string, triggerData?: Record<string, any>, isTest?: boolean) {
    return (async () => {
      const trigger = triggerData ? await this.maybeEncrypt(triggerData) : null;
      const emptyContext = await this.maybeEncrypt({});
      return this.client
        .from('taskflow_executions')
        .insert({
          taskflowId,
          triggerData: trigger,
          startedAt: new Date(),
          context: emptyContext,
          status: 'RUNNING',
          isTest: isTest || false,
        })
        .select('id')
        .single();
    })();
  }

  updateExecution(executionId: string, updates: Record<string, any>) {
    return (async () => {
      const processed: Record<string, any> = { ...updates };
      if (processed.triggerData) {
        processed.triggerData = await this.maybeEncrypt(processed.triggerData);
      }
      if (processed.context) {
        processed.context = await this.maybeEncrypt(processed.context);
      }
      if (processed.result) {
        processed.result = await this.maybeEncrypt(processed.result);
      }
      return this.client
        .from('taskflow_executions')
        .update(processed)
        .eq('id', executionId);
    })();
  }

  getExecution(executionId: string) {
    return this.client
      .from('taskflow_executions')
      .select(
        `id, taskflowId, triggerData, context, result, status, startedAt, updatedAt, completedAt, isTest`
      )
      .eq('id', executionId)
      .single()
      .then(async res => {
        if (res.data) {
          res.data.triggerData = await this.maybeDecrypt(res.data.triggerData);
          res.data.context = await this.maybeDecrypt(res.data.context);
          res.data.result = await this.maybeDecrypt(res.data.result);
        }
        return res;
      });
  }

  listExecutions(taskflowId: string) {
    return this.client
      .from('taskflow_executions')
      .select(
        `id, taskflowId, triggerData, context, result, status, startedAt, updatedAt, completedAt, isTest`
      )
      .eq('taskflowId', taskflowId)
      .order('updatedAt', { ascending: false })
      .then(async res => {
        if (res.data) {
          for (const row of res.data) {
            row.triggerData = await this.maybeDecrypt(row.triggerData);
            row.context = await this.maybeDecrypt(row.context);
            row.result = await this.maybeDecrypt(row.result);
          }
        }
        return res;
      });
  }

  getConnection(providerKey: string, userId: string) {
    return this.client
      .from('connections')
      .select('id')
      .eq('providerKey', providerKey)
      .eq('userId', userId)
      .maybeSingle();
  }

  getExecutionContext(executionId: string) {
    return this.client
      .from('taskflow_executions')
      .select('context')
      .eq('id', executionId)
      .single()
      .then(async res => {
        if (res.data) {
          res.data.context = await this.maybeDecrypt(res.data.context);
        }
        return res;
      });
  }

  createTaskflow(schema: any, conversationId: string, active: boolean) {
    return this.client
      .from('taskflows')
      .insert({ schema, conversationId, active })
      .select('id, schema, active')
      .single();
  }

  createSyncTrigger(params: {
    taskflowId: string;
    providerKey: string;
    model: string;
    syncKey: string;
    condition: any;
    syncScope: any;
  }) {
    return this.client.from('sync_triggers').insert(params);
  }

  createExecutionTrace(executionId: string, trace: any[]) {
    return (async () => {
      const encrypted = await this.maybeEncrypt(trace);
      return this.client
        .from('execution_traces')
        .insert({ executionId, trace: encrypted });
    })();
  }

  getExecutionTrace(executionId: string) {
    return this.client
      .from('execution_traces')
      .select('executionId, trace')
      .eq('executionId', executionId)
      .maybeSingle()
      .then(async res => {
        if (res.data) {
          res.data.trace = await this.maybeDecrypt(res.data.trace);
        }
        return res;
      });
  }
}

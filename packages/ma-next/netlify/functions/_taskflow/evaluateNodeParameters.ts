import { Tournament } from '@n8n/tournament/dist/index.js';

/**
 * Resolves parameters using the tournament e.g.:
 * { "helloWorld": "{{trigger.hello}}" }       => { "helloWorld": "world" }
 * { "someValue": "={{node1.someValue}}"       => { "someValue": value } (literal value, not string)
 */
function evaluateNodeParameters(
  parameters: Record<string, any>,
  input: Record<string, any>,
  tournament: Tournament
): Record<string, any> {
  if (Array.isArray(parameters)) {
    return parameters.map(item => handleValue(item, input, tournament));
  }

  const resolved: Record<string, any> = {};
  for (const [key, value] of Object.entries(parameters)) {
    resolved[key] = handleValue(value, input, tournament);
  }
  return resolved;
}

/**
 * Handles a single value in the parameters object
 * Recursively processes nested objects and arrays
 */
function handleValue(value: any, input: Record<string, any>, tournament: Tournament) {
  if (typeof value === 'string') {
    const template = value.startsWith('=') ? value.substring(1) : value;

    return tournament.execute(template, input);
  } else if (typeof value === 'object' && value !== null) {
    return evaluateNodeParameters(value, input, tournament);
  } else {
    return value;
  }
}

export { evaluateNodeParameters, handleValue };

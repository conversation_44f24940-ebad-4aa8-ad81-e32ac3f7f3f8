import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { maybeDecrypt } from '../_shared/crypto';

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'GET') {
    return validationError('Method not allowed');
  }

  const [authError, user] = await initAuthenticate(req);
  if (authError) return authError;

  const supabase = initServiceRoleSupabase();
  const { data, error } = await supabase
    .from('profiles')
    .select('id, firstName, lastName, preferences')
    .eq('id', user.id)
    .single();

  if (error || !data) return errorResponse(error || 'Profile not found');

  data.firstName = await maybeDecrypt(data.firstName);
  data.lastName = await maybeDecrypt(data.lastName);

  return new Response(JSON.stringify(data), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

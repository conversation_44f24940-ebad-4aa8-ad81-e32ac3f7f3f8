import { deepStrictEqual } from 'node:assert';
import { test } from 'node:test';
import { TaskflowToRawTransformer } from './TaskflowToRawTransformer';

test('converts schema with triggers and connection steps back to raw', () => {
  const schema = {
    steps: [
      { type: 'connectProvider', provider: 'gmail', completed: false },
      { type: 'other', foo: 'bar' }
    ],
    nodes: [ { id: 'n1', type: 'ai.simple', parameters: {} } ],
    triggers: [ { id: 't1', type: 'trigger.syncTrigger', parameters: { providerKey: 'gmail' } } ]
  };

  const transformer = new TaskflowToRawTransformer(JSON.parse(JSON.stringify(schema)));
  const raw = transformer.mergeTriggersIntoNodes().removeConnectProviderSteps().getSchema();

  deepStrictEqual(raw, {
    steps: [ { type: 'other', foo: 'bar' } ],
    nodes: [
      { id: 'n1', type: 'ai.simple', parameters: {} },
      { id: 't1', type: 'trigger.syncTrigger', parameters: { providerKey: 'gmail' } }
    ]
  });
});

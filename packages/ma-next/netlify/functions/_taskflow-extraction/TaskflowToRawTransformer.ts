export class TaskflowToRawTransformer {
  private schema: any;

  constructor(schema: any) {
    this.schema = schema;
  }

  mergeTriggersIntoNodes() {
    if (Array.isArray(this.schema.triggers)) {
      this.schema.nodes = [
        ...this.schema.triggers,
        ...(this.schema.nodes || [])
      ];
      delete this.schema.triggers;
    }
    return this;
  }

  removeConnectProviderSteps() {
    if (Array.isArray(this.schema.steps)) {
      this.schema.steps = this.schema.steps.filter(
        (s: any) => s.type !== 'connectProvider'
      );
      if (this.schema.steps.length === 0) {
        delete this.schema.steps;
      }
    }
    return this;
  }

  getSchema() {
    return this.schema;
  }
}

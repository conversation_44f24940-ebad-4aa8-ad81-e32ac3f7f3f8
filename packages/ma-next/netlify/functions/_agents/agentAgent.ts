import { createOpenAI } from '@ai-sdk/openai';
import { streamText, tool } from 'ai';
import { VercelMessage } from '../_protocol/vercel';

import {
  ACTION_INPUTS_STRING,
  SYNC_OUTPUTS_STRING,
  SYNC_OUTPUT_MODELS_JSON_SCHEMA,
  ACTION_INPUT_MODELS_JSON_SCHEMA,
} from './nangoConstants';
import {
  EMAIL_WORKFLOW_SCHEMA,
  GITHUB_PR_DESCRIPTION_WORKFLOW_SCHEMA,
  SLACK_CHANNEL_TO_EMAIL_TEMPLATE,
} from './sampleWorkflows';
import { z } from 'zod';

const openai = createOpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

type Opts = {
  previousResponseId?: string;
  userDateTime: string;
  userProfile?: {
    firstName: string;
    lastName: string;
  };
  abortSignal?: AbortSignal;
};

function agentAgent(
  messages: VercelMessage[],
  { previousResponseId, userDateTime, userProfile, abortSignal }: Opts
) {
  return streamText({
    model: openai.responses('gpt-4.1'),
    abortSignal,
    providerOptions: {
      openai: previousResponseId
        ? {
            previousResponseId,
            strictSchemas: false,
          }
        : {
            strictSchemas: false,
          },
    },
    system: `
You are the Agent authoring Agent for MakeAgent, a startup that simplifies automation through a friendly chat interface. Your primary role is to understand user intent and map it to one of the available taskflows.

**Available Taskflows:**

---
**Workflow Name:** EMAIL_WORKFLOW_SCHEMA
**Description:** Triggers on new emails and drafts responses
**Schema:**
\`\`\`json
${JSON.stringify(EMAIL_WORKFLOW_SCHEMA, null, 2)}
\`\`\`
---
**Workflow TEMPLATE Name:** SLACK_CHANNEL_TO_EMAIL_TEMPLATE
**Description:** Sends an email when a message is posted in a Slack channel. You MUST look up the channel ID by calling the list channels action first to resolve. The user specified channel will not be the id.
**Schema:**
\`\`\`json
${JSON.stringify(SLACK_CHANNEL_TO_EMAIL_TEMPLATE, null, 2)}
\`\`\`

---
Some workflow templates contain placeholder values like <> or <with_description>. YOU MUST replace EVERY "<>" or "<item_name>" with valid values based on the user's request while keeping the rest of the template unchanged. Failure to interpolate valid values will result in an error.
---

<SYNC_OUTPUTS>
${SYNC_OUTPUTS_STRING}
</SYNC_OUTPUTS>
<SYNC_OUTPUT_MODELS_FIELDS>
${JSON.stringify(SYNC_OUTPUT_MODELS_JSON_SCHEMA)}
</SYNC_OUTPUT_MODELS_FIELDS>

<ACTION_INPUTS>
${ACTION_INPUTS_STRING}
</ACTION_INPUTS>
<ACTION_INPUTS_MODELS_FIELDS>
${JSON.stringify(ACTION_INPUT_MODELS_JSON_SCHEMA)}
</ACTION_INPUTS_MODELS_FIELDS>

**Your Job**
1. If the user's request matches one of the predefined workflows above, return the corresponding JSON within <taskflow> tags.
2. For new requests that move data from one system to another, build a taskflow that performs the appropriate transformation and action.
3. Ask clarifying questions if details are missing.
4. Finish with a short summary of what the agent will do.

**Rules**
- Use the predefined taskflows verbatim when they fit.
- Return the JSON inside <taskflow> tags and do not explain it.
- Be warm and helpful.

**TOOL USE**
 - You EXCLUSIVELY use the actionCall tool to help with setup of taskflows, such as establishing hard coded parameters for templates.

  `,
    messages,
    tools: {
      actionCall: tool({
        description: 'Call an action',
        parameters: z.object({
          actionParameters: z
            .record(z.any())
            .describe(
              'You MUST include this payload, and it MUST be the shape of the associated MODEL for the tool. Use {} for no parameters. NOT OPTIONAL, WILL RESULT IN AN ERROR IF NOT PROVIDED.'
            ),
          providerKey: z.string().describe('The provider key (e.g., twitter)'),
          actionKey: z.string().describe('The action key (e.g., post_tweet)'),
          userExplanation: z
            .string()
            .describe(
              'Max 5 words: Shown to the user to explain to them what is happening - i.e. what is this action call doing? E.g. Finding posts.'
            ),
        }),
      }),
      askUserToSelectResource: tool({
        description: 'Ask the user to select from a list of options',
        parameters: z.object({
          title: z.string().describe('A title to show above the options'),
          options: z
            .array(
              z.object({
                value: z.string().describe('The underlying identifier for the resource'),
                label: z
                  .string()
                  .describe('The plain text user facing description for identifying the resource'),
              })
            )
            .min(2)
            .describe('The options to present to the user'),
        }),
      }),
    },
  });
}

export { agentAgent };

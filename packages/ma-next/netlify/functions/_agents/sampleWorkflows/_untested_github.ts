// This file contains workflow schemas for different agent types

/**
 * GitHub PR Description Bot Workflow Schema
 * Triggers on new PRs and generates descriptions based on file changes
 */
export const GITHUB_PR_DESCRIPTION_WORKFLOW_SCHEMA = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      parameters: {
        providerKey: 'github',
        model: 'PullRequest',
        syncKey: 'pull-requests',
        label: 'New Pull Request Trigger',
        description: 'Triggers when a new pull request is created',
      },
    },
    {
      id: 'node1',
      type: 'provider.github.get-pull-request-files',
      parameters: {
        owner: '{{trigger.repository.owner.login}}',
        repo: '{{trigger.repository.name}}',
        pull_number: '{{trigger.number}}',
      },
    },
    {
      id: 'node2',
      type: 'ai.simple',
      parameters: {
        model: 'gpt-4o',
        system:
          'You are a technical writer who specializes in creating clear, concise pull request descriptions. Analyze the file changes and create a professional PR description that explains the changes made, their purpose, and any important implementation details.',
        prompt:
          "Generate a detailed pull request description based on the following file changes:\n\n{{node1.files.map(file => `File: ${file.filename}\\nStatus: ${file.status}\\nAdditions: ${file.additions}\\nDeletions: ${file.deletions}\\n${file.patch ? 'Patch: ' + file.patch : ''}\\n\\n`).join('')}}",
        outputSchema: {
          type: 'object',
          properties: {
            title: {
              type: 'string',
              description: 'A concise title for the pull request',
            },
            description: {
              type: 'string',
              description: 'A detailed description of the changes in the pull request',
            },
            changeType: {
              type: 'string',
              enum: ['feature', 'bugfix', 'refactor', 'documentation', 'other'],
              description: 'The type of change in this pull request',
            },
          },
          required: ['title', 'description', 'changeType'],
        },
      },
    },
    {
      id: 'node3',
      type: 'provider.github.update-pull-request',
      parameters: {
        owner: '{{trigger.repository.owner.login}}',
        repo: '{{trigger.repository.name}}',
        pull_number: '{{trigger.number}}',
        title: '{{node2.title}}',
        body: '{{node2.description}}',
        requiresConfirmation: true,
      },
    },
  ],
};

export const GOOGLE_DRIVE_TO_DROPBOX = {
  "nodes": [
    {
      "id": "trigger1",
      "type": "trigger.syncTrigger",
      "parameters": {
        "label": "New Google Drive Document Trigger",
        "model": "Document",
        "syncKey": "documents-fork",
        "description": "Triggers when a new document is added to Google Drive",
        "providerKey": "google-drive"
      }
    },
    {
      "id": "dropbox-upload",
      "type": "provider.dropbox.upload-file",
      "parameters": {
        "mode": "add",
        "mute": false,
        "path": "/gdrive-uploads/{{trigger.accessible.fileName}}",
        "content": "={{trigger.accessible}}",
        "encoding": "base64",
        "autorename": true
      }
    }
  ]
}

export const GMAIL_TO_SPREADSHEET_TEMPLATE = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      condition: {
        and: [
          {
            in: [
              'INBOX',
              {
                var: 'labels',
              },
            ],
          },
          {
            in: [
              {
                var: 'subject',
                '#promotion',
              },
            ],
          },
        ],
      },
      parameters: {
        label: 'New Email Trigger',
        model: 'GmailEmail',
        syncKey: 'emails-fork',
        description: 'Triggers when a new email is received',
        providerKey: 'google-mail',
      },
    },
    {
      id: 'extract1',
      type: 'ai.simple',
      parameters: {
        prompt:
          '\n\nFrom: {{trigger.sender}}\nSubject: {{trigger.subject}}\nBody: {{trigger.body}}\n\n',
        system:
          'You are an information extraction assistant. Extract the following fields from the email below: 1) Email address of the sender, 2) Name of the sender, 3) Date of Birth (DOB) if present, 4) Any comments or additional information. Return as a JSON object with keys: Email, Name, DOB, Comments. If a field is missing, return an empty string for that field.',
        outputSchema: {
          type: 'object',
          required: ['Email', 'Name', 'DOB', 'Comments'],
          properties: {
            DOB: {
              type: 'string',
            },
            Name: {
              type: 'string',
            },
            Email: {
              type: 'string',
            },
            Comments: {
              type: 'string',
            },
          },
        },
      },
    },
    {
      id: 'sheet1',
      type: 'provider.google-sheet.append-sheet',
      parameters: {
        range: 'Submissions!A:D',
        values: [
          ['{{extract1.Email}}', '{{extract1.Name}}', '{{extract1.DOB}}', '{{extract1.Comments}}'],
        ],
        spreadsheetId: '1YfIkVaMh9jSICaGqrtGHuLq5pLWmwXSX3RHMDHeVqPg',
      },
    },
  ],
};

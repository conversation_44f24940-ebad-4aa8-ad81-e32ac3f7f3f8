export const GMAIL_ATTACHMENTS_TO_DROPBOX_TEMPLATE = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      condition: {
        and: [
          {
            in: ['INBOX', { var: 'labels' }],
          },
          {
            '>': [{ var: 'attachments.length' }, 0],
          },
        ],
      },
      parameters: {
        label: 'New Email with Attachment Trigger',
        model: 'GmailEmail',
        syncKey: 'emails-fork',
        description: 'Triggers when a new email with attachments is received',
        providerKey: 'google-mail',
      },
    },
    {
      id: 'dropbox-upload',
      type: 'provider.dropbox.upload-file',
      parameters: {
        mode: 'add',
        mute: false,
        path: '/email-attachments/{{trigger.attachments[0].filename',
        content: '={{trigger.attachments[0].accessible}}',
        encoding: 'raw',
        autorename: true,
      },
    },
  ],
};

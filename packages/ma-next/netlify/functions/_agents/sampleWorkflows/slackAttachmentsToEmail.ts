/**
 * Slack Channel Alert Workflow
 * Sends an email when a new message is posted in a specific Slack channel
 */
export const SLACK_CHANNEL_TO_EMAIL_TEMPLATE = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      parameters: {
        providerKey: 'slack',
        model: 'SlackSyncMessage',
        syncKey: 'messages',
        description: '<>',
        syncScopes: { conversations: ['<channel_id>'] },
      },
      condition: {
        and: [
          {
            '==': [
              {
                var: 'channel_id',
              },
              '<channel_id>',
            ],
          },
          {
            '>': [
              {
                var: 'message.files.length',
              },
              0,
            ],
          },
        ],
      },
    },
    {
      id: 'node1',
      type: 'provider.google-mail.send-email',
      parameters: {
        to: '<>',
        body: '<>',
        subject: '<>',
        attachments: '={{trigger.message.files.map(file => file.accessible)}}',
      },
    },
  ],
};

/**
 * Email Assistant Workflow Schema
 * Triggers on new emails and drafts responses
 */
export const EMAIL_WORKFLOW_SCHEMA = {
  nodes: [
    {
      id: 'trigger1',
      type: 'trigger.syncTrigger',
      parameters: {
        providerKey: 'google-mail',
        model: 'GmailEmail',
        syncKey: 'emails-fork',
        label: 'New Email Trigger',
        description: 'Triggers when a new email is received',
      },
      condition: {
        in: ['INBOX', { var: 'labels' }],
      },
    },
    {
      id: 'node1',
      type: 'ai.simple',
      parameters: {
        system:
          "You are an email assistant that helps draft responses to incoming emails on my behalf. Be professional and concise. Draft a response to the email presented below on my behalf. Use the sender's name in the greeting and include a closing signature with my name.",
        prompt:
          '\n\nFrom: {{trigger.sender}}\nSubject: {{trigger.subject}}\nBody: {{trigger.body}}\n\n',
        outputSchema: {
          type: 'object',
          properties: {
            subject: {
              type: 'string',
              description: 'The subject line for the response email',
            },
            body: {
              type: 'string',
              description: 'The body content of the response email',
            },
          },
          required: ['subject', 'body'],
        },
      },
    },
    {
      id: 'node2',
      type: 'provider.google-mail.compose-draft-reply',
      parameters: {
        replyBody: '{{node1.body}}',
        sender: '{{trigger.sender.match(/[^<]+<([^>]+)>/)?.[1] || trigger.sender}}',
        subject: '{{trigger.subject}}',
        body: '{{trigger.body}}',
        threadId: '{{trigger.threadId}}',
        messageId: '{{trigger.messageId}}',
        inReplyTo: '{{trigger.inReplyTo}}',
        references: '{{trigger.references}}',
        date: '{{trigger.date}}',
      },
    },
  ],
};

import { getNodeSchemas, JsonSchema } from './nodeSchemas';
import { validateParameterTemplates, evaluateTemplate } from './paramEvaluation';

function resolveEqualsTemplates(params: any, schema: JsonSchema): any {
  if (typeof params === 'string') {
    if (params.startsWith('=') && params.includes('{{') && params.includes('}}')) {
      return evaluateTemplate(params.slice(1), schema);
    }
    return params;
  } else if (Array.isArray(params)) {
    return params.map(v => resolveEqualsTemplates(v, schema));
  } else if (params && typeof params === 'object') {
    const out: Record<string, any> = {};
    for (const [k, v] of Object.entries(params)) out[k] = resolveEqualsTemplates(v, schema);
    return out;
  }
  return params;
}

export function validateTaskflowSchema(definition: any): boolean {
  const contextSchema: JsonSchema = { type: 'object', properties: {} };
  let first = true;
  for (const node of definition.nodes) {
    const schemas = getNodeSchemas(node.type);
    const resolvedParams = resolveEqualsTemplates(node.parameters, contextSchema);
    schemas.parameters.parse(resolvedParams);
    const keys = Object.keys(contextSchema.properties || {});
    validateParameterTemplates(node.parameters, contextSchema, keys);
    const output = schemas.output(node.parameters);
    const key = first && node.type.startsWith('trigger.') ? 'trigger' : node.id;
    contextSchema.properties![key] = output;
    first = false;
  }
  return true;
}

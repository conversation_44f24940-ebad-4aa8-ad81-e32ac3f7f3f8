import { test } from 'node:test';
import { strictEqual, throws } from 'node:assert';
import { EMAIL_WORKFLOW_SCHEMA, SLACK_CHANNEL_TO_EMAIL_TEMPLATE } from '../_agents/sampleWorkflows';
import { validateTaskflowSchema } from './schemaValidator';

// Successful validation of sample workflow
test('validate email workflow schema', () => {
  const result = validateTaskflowSchema(EMAIL_WORKFLOW_SCHEMA);
  strictEqual(result, true);
});

// Failure when referencing unknown trigger property
test('fails on bad template reference', () => {
  const bad = JSON.parse(JSON.stringify(EMAIL_WORKFLOW_SCHEMA));
  bad.nodes[1].parameters.prompt = '{{trigger.missing}}';
  throws(() => validateTaskflowSchema(bad));
});

// Validation of workflow using ={{}} syntax
test('validate workflow with equals template', () => {
  const wf = JSON.parse(JSON.stringify(SLACK_CHANNEL_TO_EMAIL_TEMPLATE));
  const result = validateTaskflowSchema(wf);
  strictEqual(result, true);
});

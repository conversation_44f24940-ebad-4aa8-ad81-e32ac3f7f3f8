import type {
  NangoAction,
  GithubDeleteRepositoryOutput,
  GithubRepositoryInput,
  GithubRepository,
} from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GithubRepositoryInput
): Promise<GithubDeleteRepositoryOutput | NangoError> {
  try {
    const { owner, repo } = input;

    if (!owner || !repo) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Repository owner and name are required',
        },
      };
    }

    const repoResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/repos/${owner}/${repo}`,
    });

    await nango.proxy({
      method: 'DELETE',
      endpoint: `/repos/${owner}/${repo}`,
    });

    return {
      repository: repoResponse.data as GithubRepository,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error('Error deleting repository:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.message ||
      error?.message ||
      'An unknown error occurred while deleting the repository.';
    return { error: { status, message } };
  }
}

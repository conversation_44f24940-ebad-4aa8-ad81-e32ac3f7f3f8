import type {
  NangoAction,
  LinearIssueInput,
  LinearDeleteIssueOutput,
  LinearIssue,
} from '../models';

type ActionError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: LinearIssueInput
): Promise<LinearDeleteIssueOutput | ActionError> {
  try {
    if (!input.issueId) {
      return {
        error: {
          status: 400,
          message: 'Missing required parameter: issueId is required',
        },
      };
    }

    const issueQuery = `
            query GetIssue($issueId: String!) {
                issue(id: $issueId) {
                    id
                    title
                    description
                    number
                    priority
                    url
                    createdAt
                    updatedAt
                    state { id name color type }
                    assignee { id name email avatarUrl }
                    team { id name key }
                    project { id name icon color }
                    labels { nodes { id name color } }
                }
            }
        `;

    const issueResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: issueQuery,
        variables: { issueId: input.issueId },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (issueResponse.data?.errors && issueResponse.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${issueResponse.data.errors[0].message}`;
      console.error('Error fetching issue from Linear:', errorMessage);
      const status = issueResponse.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!issueResponse.data?.data?.issue) {
      const status = issueResponse.status === 200 ? 404 : issueResponse.status || 404;
      const message = `Issue with ID ${input.issueId} not found.`;
      console.error('Error fetching issue from Linear:', message);
      return { error: { status, message } };
    }

    const issueRaw = issueResponse.data.data.issue;
    const issue: LinearIssue = {
      id: issueRaw.id,
      title: issueRaw.title,
      description: issueRaw.description || '',
      number: issueRaw.number,
      priority: issueRaw.priority,
      url: issueRaw.url,
      createdAt: issueRaw.createdAt,
      updatedAt: issueRaw.updatedAt,
      state: {
        id: issueRaw.state.id,
        name: issueRaw.state.name,
        color: issueRaw.state.color,
        type: issueRaw.state.type,
      },
      team: {
        id: issueRaw.team.id,
        name: issueRaw.team.name,
        key: issueRaw.team.key,
      },
      labels:
        issueRaw.labels?.nodes.map((label: any) => ({
          id: label.id,
          name: label.name,
          color: label.color,
        })) || [],
    };
    if (issueRaw.assignee) {
      issue.assignee = {
        id: issueRaw.assignee.id,
        name: issueRaw.assignee.name,
        email: issueRaw.assignee.email,
        avatarUrl: issueRaw.assignee.avatarUrl,
      };
    }
    if (issueRaw.project) {
      issue.project = {
        id: issueRaw.project.id,
        name: issueRaw.project.name,
        icon: issueRaw.project.icon,
        color: issueRaw.project.color,
      };
    }

    const mutation = `
            mutation IssueDelete($issueId: String!) {
                issueDelete(id: $issueId) {
                    success
                }
            }
        `;

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/graphql',
      data: {
        query: mutation,
        variables: { issueId: input.issueId },
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.data?.errors && response.data.errors.length > 0) {
      const errorMessage = `GraphQL Error: ${response.data.errors[0].message}`;
      console.error('Error deleting issue from Linear:', errorMessage);
      const status = response.status || 400;
      return { error: { status, message: errorMessage } };
    }

    if (!response.data?.data?.issueDelete?.success) {
      const status = response.status || 404;
      const message = `Failed to delete issue (success: ${response.data?.data?.issueDelete?.success}). Issue might not exist or another error occurred. Response: ${JSON.stringify(response.data)}`;
      console.error('Error deleting issue from Linear:', message);
      return { error: { status, message } };
    }

    if (response.status >= 400) {
      const status = response.status;
      const message = `Failed to delete issue. Status: ${status}. Response: ${JSON.stringify(response.data)}`;
      console.error('Error deleting issue from Linear:', message);
      return { error: { status, message } };
    }

    return {
      issue,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error('Error deleting issue from Linear:', error);

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message ||
      error.message ||
      'An unknown error occurred while deleting the issue.';

    return {
      error: {
        status: status,
        message: `Failed to delete issue: ${message}`,
      },
    };
  }
}

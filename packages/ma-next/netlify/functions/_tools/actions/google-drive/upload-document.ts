import type { NangoAction, GoogleDriveDocument, GoogleDriveFileUploadInput } from '../models';

interface NangoError {
  error: {
    status: number;
    message: string;
  };
}

export default async function runAction(
  nango: NangoAction,
  input: GoogleDriveFileUploadInput
): Promise<GoogleDriveDocument | NangoError> {
  if (!input.content) {
    return {
      error: { status: 400, message: 'Input validation failed: File content is required.' },
    };
  }
  if (!input.name) {
    return { error: { status: 400, message: 'Input validation failed: File name is required.' } };
  }

  const mimeType = input.mimeType || 'application/octet-stream';

  try {
    const fileContent =
      input.isBase64 === true ? Buffer.from(input.content, 'base64') : Buffer.from(input.content);
    const fileSizeInBytes = fileContent.length;
    const maxFileSizeInBytes = 5 * 1024 * 1024; // 5 MB

    if (fileSizeInBytes > maxFileSizeInBytes) {
      return {
        error: {
          status: 400,
          message:
            'File size exceeds limit: The file size exceeds the 5 MB limit for simple uploads.',
        },
      };
    }

    const uploadResponse = await nango.post<GoogleDriveDocument>({
      endpoint: 'upload/drive/v3/files',
      params: { uploadType: 'media' },
      headers: {
        'Content-Type': mimeType,
        'Content-Length': fileContent.length.toString(),
      },
      data: fileContent,
      retries: 10,
    });

    if (uploadResponse.status !== 200 && uploadResponse.status !== 201) {
      return {
        error: {
          status: uploadResponse.status,
          message: `Failed to upload file: Status ${uploadResponse.status}`,
        },
      };
    }

    const fileId = uploadResponse.data.id;
    if (input.folderId || input.name || input.description) {
      const metadata: Record<string, any> = { name: input.name };
      if (input.description) {
        metadata['description'] = input.description;
      }
      const updateResponse = await nango.patch<GoogleDriveDocument>({
        endpoint: `drive/v3/files/${fileId}`,
        params: {
          ...(input.folderId ? { addParents: input.folderId, removeParents: 'root' } : {}),
          supportsAllDrives: 'true',
        },
        headers: { 'Content-Type': 'application/json' },
        data: metadata,
        retries: 3,
      });
      if (updateResponse.status !== 200) {
        return {
          error: {
            status: updateResponse.status,
            message: `Failed to update file metadata: Status Code ${updateResponse.status}`,
          },
        };
      }
      return updateResponse.data;
    }

    return uploadResponse.data;
  } catch (error: any) {
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while uploading file to Google Drive.';
    return { error: { status, message } };
  }
}

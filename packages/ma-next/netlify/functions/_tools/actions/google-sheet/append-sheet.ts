import type { NangoAction, GoogleSheetAppendOutput, GoogleSheetAppendInput } from '../models';

type NangoError = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: GoogleSheetAppendInput
): Promise<GoogleSheetAppendOutput | NangoError> {
  const { spreadsheetId, range, values } = input;

  if (!spreadsheetId || !range || !values || !Array.isArray(values)) {
    return {
      error: {
        status: 400,
        message: 'Input validation failed: spreadsheetId, range and values are required.',
      },
    };
  }

  try {
    const response = await nango.proxy({
      method: 'POST',
      endpoint: `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${encodeURIComponent(
        range
      )}:append`,
      data: { values },
      params: {
        valueInputOption: 'USER_ENTERED',
        insertDataOption: 'INSERT_ROWS',
        includeValuesInResponse: 'true',
      },
      retries: 3,
    });

    return response.data as GoogleSheetAppendOutput;
  } catch (error: any) {
    console.error('Error appending values to sheet:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error?.message ||
      'An unknown error occurred while appending to the sheet.';
    return { error: { status, message } };
  }
}

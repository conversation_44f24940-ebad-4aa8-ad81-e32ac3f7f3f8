import type { NangoAction, UrlAccessibleFile } from '../../models';

function extractPath(obj: any, path?: string): any {
  if (!path) return obj;
  return path.split('.').reduce((acc: any, key) => acc?.[key], obj);
}

function bufferFromData(data: any, encoding?: BufferEncoding): Buffer {
  if (typeof data === 'string') {
    return Buffer.from(data, encoding);
  }
  if (data instanceof ArrayBuffer) {
    return Buffer.from(data);
  }
  return Buffer.from(String(data), encoding);
}

export async function fetchAccessible(
  nango: NangoAction,
  file: UrlAccessibleFile,
): Promise<Buffer> {
  const responseType = file.responseType || 'arraybuffer';

  if (file.authentication) {
    if (file.authentication.providerKey === 'slack') {
      const connection = await nango.getConnection('slack', file.authentication.connectionId);
      if (!('raw' in connection.credentials)) {
        throw new Error('Slack credentials missing');
      }
      const raw = (connection.credentials as any)['raw'];
      const token = raw?.['authed_user']?.['access_token'];
      if (!token) {
        throw new Error('Slack user token missing');
      }
      const res = await fetch(file.url, { headers: { Authorization: `Bearer ${token}` } });
      if (!res.ok) {
        throw new Error(`Failed to fetch file: ${res.statusText}`);
      }
      return parseFetchResponse(res, responseType, file);
    }

    const res = await nango.proxy({
      method: 'GET',
      endpoint: file.url,
      connectionId: file.authentication.connectionId,
      providerConfigKey: file.authentication.providerKey,
      responseType: responseType as any,
      baseUrlOverride: '',
    });
    return parseProxyData(res.data, responseType, file);
  }

  const res = await fetch(file.url);
  if (!res.ok) {
    throw new Error(`Failed to fetch file: ${res.statusText}`);
  }
  return parseFetchResponse(res, responseType, file);
}

async function parseFetchResponse(res: Response, type: string, file: UrlAccessibleFile): Promise<Buffer> {
  if (type === 'json') {
    const data = await res.json();
    const value = extractPath(data, file.responseDataPath);
    const encoding = file.responseEncoding as BufferEncoding | undefined;
    return bufferFromData(value, encoding);
  }
  if (type === 'text') {
    const text = await res.text();
    return Buffer.from(text, (file.responseEncoding as BufferEncoding) || 'utf8');
  }
  const arr = await res.arrayBuffer();
  return Buffer.from(arr);
}

function parseProxyData(data: any, type: string, file: UrlAccessibleFile): Buffer {
  if (type === 'json') {
    const value = extractPath(data, file.responseDataPath);
    const encoding = file.responseEncoding as BufferEncoding | undefined;
    return bufferFromData(value, encoding);
  }
  if (type === 'text') {
    return Buffer.from(data, (file.responseEncoding as BufferEncoding) || 'utf8');
  }
  return Buffer.from(data);
}

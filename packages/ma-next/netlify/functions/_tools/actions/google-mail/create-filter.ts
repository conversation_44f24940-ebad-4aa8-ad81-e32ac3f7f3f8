import type { NangoAction, GmailCreateFilterInput, GmailFilter } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailCreateFilterInput
): Promise<GmailFilter | NangoError> {
  const { criteria, action } = input;

  try {
    if (!criteria && !action) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: criteria or action must be provided.',
        },
      };
    }

    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/gmail/v1/users/me/settings/filters',
      data: { criteria, action },
    });

    return response.data as GmailFilter;
  } catch (error: any) {
    console.error('Error creating filter:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while creating the filter.';
    return { error: { status, message } };
  }
}

import type {
  NangoAction,
  HarvestTimeEntryInput,
  HarvestDeleteTimeEntryOutput,
} from '../models';
import { getHarvestAccountId } from './utils/harvestHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: HarvestTimeEntryInput
): Promise<HarvestDeleteTimeEntryOutput | NangoError> {
  try {
    if (!input.timeEntryId) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Missing required parameter: timeEntryId is required',
        },
      };
    }

    const accountId = await getHarvestAccountId(nango);
    if (typeof accountId !== 'string') {
      return accountId as NangoError;
    }

    const fetchResponse = await nango.proxy({
      method: 'GET',
      endpoint: `/v2/time_entries/${input.timeEntryId}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    await nango.proxy({
      method: 'DELETE',
      endpoint: `/v2/time_entries/${input.timeEntryId}`,
      headers: {
        'Harvest-Account-Id': accountId,
      },
    });

    return {
      timeEntry: fetchResponse.data,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error('Error deleting time entry from Harvest:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while deleting the time entry.';

    return { error: { status, message } };
  }
}

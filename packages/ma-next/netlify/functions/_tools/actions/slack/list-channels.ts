import type { NangoAction, SlackListChannelsInput, SlackConversationsList } from '../models';

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackListChannelsInput | undefined
): Promise<SlackConversationsList | ErrorResponse> {
  const currentInput = input || {};
  const searchName = currentInput.channelSearch?.replace(/^#/, '');
  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const baseParams: { [key: string]: string | number } = {};
    if (currentInput.types !== undefined) {
      baseParams['types'] = currentInput.types;
    }

    if (searchName) {
      let cursor = currentInput.cursor;
      const limit = currentInput.limit ?? 100;
      while (true) {
        const params = { ...baseParams, limit } as { [key: string]: string | number };
        if (cursor !== undefined) {
          params['cursor'] = cursor;
        }
        const config = {
          method: 'GET' as const,
          baseUrlOverride: 'https://slack.com/api',
          endpoint: 'conversations.list',
          params,
          headers: {
            Authorization: `Bearer ${userToken}`,
          },
          retries: 3,
        };
        const response = await nango.proxy<SlackConversationsList>(config);
        if (!response.data.ok) {
          const errorMessage =
            response.data.error || 'Unknown error listing Slack channels';
          const status = response.status ?? 400;
          console.error(errorMessage);
          return { error: { status, message: errorMessage } };
        }
        const found = response.data.channels.find(
          (c: any) => c.name === searchName
        );
        if (found) {
          return { ok: true, channels: [found] };
        }
        cursor = response.data.response_metadata?.next_cursor;
        if (!cursor) {
          break;
        }
      }
      return {
        error: { status: 404, message: `Channel not found: ${currentInput.channelSearch}` },
      };
    }

    const params = { ...baseParams } as { [key: string]: string | number };
    if (currentInput.limit !== undefined) {
      params['limit'] = currentInput.limit;
    }
    if (currentInput.cursor !== undefined) {
      params['cursor'] = currentInput.cursor;
    }

    const config = {
      method: 'GET' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'conversations.list',
      params,
      headers: {
        Authorization: `Bearer ${userToken}`,
      },
      retries: 3,
    };

    const response = await nango.proxy<SlackConversationsList>(config);

    return response.data;
  } catch (error: any) {
    console.error(`Error listing Slack channels: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error listing Slack channels';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      console.error('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      console.error('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    console.error(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}

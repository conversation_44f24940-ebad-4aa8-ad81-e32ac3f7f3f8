import type { NangoAction, SlackCreateChannelInput, SlackCreateChannelOutput, SlackConversation } from '../models';

interface SlackCreateChannelResponse {
  ok: boolean;
  channel?: SlackConversation;
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackCreateChannelInput
): Promise<SlackCreateChannelOutput | ErrorResponse> {
  if (!input || !input.name) {
    return {
      error: { status: 400, message: 'Channel name is required to create a Slack channel.' },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const dataPayload: { [key: string]: any } = { name: input.name };
    if (typeof input.is_private === 'boolean') {
      dataPayload['is_private'] = input.is_private;
    }

    const config = {
      method: 'POST' as const,
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'conversations.create',
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      data: dataPayload,
      retries: 3,
    };

    const response = await nango.proxy<SlackCreateChannelResponse>(config);

    if (!response.data.ok) {
      const errorMessage = `Slack API error: ${response.data.error || 'Unknown error creating channel'}`;
      console.error(errorMessage);
      const status = response.status ?? 400;
      return { error: { status, message: errorMessage } };
    }

    const result: SlackCreateChannelOutput = {
      ok: response.data.ok,
      ...(response.data.channel && { channel: response.data.channel }),
    };

    return result;
  } catch (error: any) {
    console.error(`Error creating Slack channel: ${error.message}`);
    const errorMessage = error.response?.data?.error || error.message || 'Unknown error creating Slack channel';
    const status = error.response?.status ?? 500;
    return { error: { status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      console.error('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = (raw as any)?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      console.error('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    console.error(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}

import type {
  NangoAction,
  SlackDeleteMessageInput,
  SlackDeleteMessageOutput,
  SlackMessage,
} from '../models';

interface SlackDeleteResponse {
  ok: boolean;
  channel?: string;
  ts?: string;
  error?: string;
}

type ErrorResponse = { error: { status: number; message: string } };

export default async function runAction(
  nango: NangoAction,
  input: SlackDeleteMessageInput
): Promise<SlackDeleteMessageOutput | ErrorResponse> {
  if (!input || !input.channel || !input.ts) {
    return {
      error: {
        status: 400,
        message: 'Channel ID and message timestamp (ts) are required to delete a message.',
      },
    };
  }

  try {
    const tokenResult = await getUserToken(nango);
    if (typeof tokenResult !== 'string') {
      return tokenResult;
    }
    const userToken = tokenResult;

    const messageFetch = await nango.proxy<{ ok: boolean; messages: SlackMessage[] }>({
      method: 'GET',
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'conversations.history',
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      params: {
        channel: input.channel,
        latest: input.ts,
        inclusive: 'true',
        limit: 1,
      },
      retries: 3,
    });

    if (!messageFetch.data.ok || messageFetch.data.messages.length === 0) {
      const status = messageFetch.status ?? 400;
      const errorMessage = `Failed to fetch message before deletion: ${messageFetch.data}`;
      return { error: { status, message: errorMessage } };
    }

    const message = messageFetch.data.messages[0];

    const deleteResponse = await nango.proxy<SlackDeleteResponse>({
      method: 'POST',
      baseUrlOverride: 'https://slack.com/api',
      endpoint: 'chat.delete',
      headers: {
        Authorization: `Bearer ${userToken}`,
        'Content-Type': 'application/json; charset=utf-8',
      },
      data: {
        channel: input.channel,
        ts: input.ts,
      },
      retries: 3,
    });
    if (!deleteResponse.data.ok) {
      const errorMessage = `Slack API error: ${
        deleteResponse.data.error || 'Unknown error deleting message'
      }`;
      console.error(errorMessage);
      const status = deleteResponse.status ?? 400;
      return { error: { status: status, message: errorMessage } };
    }

    return {
      message,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error(`Error deleting Slack message: ${error.message}`);
    const errorMessage =
      error.response?.data?.error || error.message || 'Unknown error deleting Slack message';
    const status = error.response?.status ?? 500;
    return { error: { status: status, message: errorMessage } };
  }
}

async function getUserToken(nango: NangoAction): Promise<string | ErrorResponse> {
  try {
    const connection = await nango.getConnection();
    if (!('raw' in connection.credentials)) {
      console.error('Raw credentials missing in Slack connection.');
      return { error: { status: 500, message: 'Raw credentials missing in Slack connection.' } };
    }
    const raw = connection.credentials['raw'];
    const userToken = raw?.['authed_user']?.['access_token'];

    if (!userToken || typeof userToken !== 'string') {
      console.error('User token not found or invalid in Slack connection credentials.');
      return {
        error: {
          status: 500,
          message: 'User token not found or invalid in Slack connection credentials.',
        },
      };
    }
    return userToken;
  } catch (err: any) {
    console.error(`Error fetching Slack connection: ${err.message}`);
    return { error: { status: 500, message: `Error fetching Slack connection: ${err.message}` } };
  }
}

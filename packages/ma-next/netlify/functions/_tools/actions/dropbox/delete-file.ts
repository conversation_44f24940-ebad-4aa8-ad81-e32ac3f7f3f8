import type { DropboxDeleteInput, DropboxDeleteResult, NangoAction } from '../models';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: DropboxDeleteInput
): Promise<DropboxDeleteResult | NangoError> {
  try {
    const { path } = input;

    if (!path) {
      return { error: { status: 400, message: 'Input validation failed: Path is required' } };
    }

    const metadataResponse = await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/get_metadata',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        path,
        include_media_info: false,
        include_deleted: false,
        include_has_explicit_shared_members: false,
      },
      retries: 3,
    });

    await nango.proxy({
      method: 'POST',
      endpoint: '/2/files/delete_v2',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        path,
      },
      retries: 3,
    });

    return {
      file: metadataResponse.data,
      deletedAt: new Date().toISOString(),
    };
  } catch (error: any) {
    console.error('Error deleting file/folder in Dropbox:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error_summary ||
      error?.message ||
      'An unknown error occurred while deleting the file/folder in Dropbox.';
    return { error: { status, message } };
  }
}

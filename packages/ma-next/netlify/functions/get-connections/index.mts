import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { maybeDecrypt } from '../_shared/crypto';

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'GET') {
    return validationError('Method not allowed');
  }

  const [authError, user] = await initAuthenticate(req);
  if (authError) return authError;

  const supabase = initServiceRoleSupabase();
  const { data, error } = await supabase
    .from('connections')
    .select('id, providerKey, displayName, metadata')
    .eq('userId', user.id);

  if (error || !data) return errorResponse(error || 'No connections');

  for (const row of data) {
    row.displayName = await maybeDecrypt(row.displayName);
    row.metadata = await maybeDecrypt(row.metadata);
  }

  return new Response(JSON.stringify(data), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

import { createCipheriv, createDecipheriv, pbkdf2Sync, randomBytes } from 'crypto';

function getKey(secret: string): Buffer {
  const salt = Buffer.from('mcp-encryption-salt');
  return pbkdf2Sync(secret.slice(0, 32), salt, 100000, 32, 'sha256');
}

function base64urlEncode(buffer: Buffer): string {
  return buffer
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=+$/, '');
}

function base64urlDecode(str: string): Buffer {
  str = str.replace(/-/g, '+').replace(/_/g, '/');
  while (str.length % 4) str += '=';
  return Buffer.from(str, 'base64');
}

async function encryptData(data: string, secret: string): Promise<string> {
  const key = getKey(secret);
  const iv = randomBytes(12);
  const cipher = createCipheriv('aes-256-gcm', key, iv);
  const encrypted = Buffer.concat([cipher.update(data, 'utf8'), cipher.final()]);
  const tag = cipher.getAuthTag();
  const combined = Buffer.concat([iv, tag, encrypted]);
  return base64urlEncode(combined);
}

async function decryptData(encrypted: string, secret: string): Promise<string> {
  const combined = base64urlDecode(encrypted);
  if (combined.length < 28) {
    throw new Error('Invalid encrypted data.');
  }
  const iv = combined.slice(0, 12);
  const tag = combined.slice(12, 28);
  const data = combined.slice(28);
  const key = getKey(secret);
  const decipher = createDecipheriv('aes-256-gcm', key, iv);
  decipher.setAuthTag(tag);
  const decrypted = Buffer.concat([decipher.update(data), decipher.final()]);
  return decrypted.toString('utf8');
}

export { encryptData, decryptData };

async function maybeEncrypt<T>(data: T, secret = process.env.TASKFLOW_ENCRYPTION_KEY): Promise<string | T> {
  if (!secret) return data;
  return encryptData(JSON.stringify(data), secret);
}

async function maybeDecrypt<T>(data: any, secret = process.env.TASKFLOW_ENCRYPTION_KEY): Promise<T | any> {
  if (!secret || data == null) return data;
  const decrypted = await decryptData(data as string, secret);
  return JSON.parse(decrypted) as T;
}

export { maybeEncrypt, maybeDecrypt };

import { corsHeaders, handleCors } from '../_shared/cors';
import { errorResponse, validationError } from '../_shared/error';
import { initAuthenticate } from '../_shared/initAuth';
import { initServiceRoleSupabase } from '../_shared/supabase';
import { TaskflowSupabaseFacade } from '../_taskflow/taskflowSupabaseFacade';

export default async function handler(req: Request) {
  const cors = handleCors(req);
  if (cors) return cors;

  if (req.method !== 'GET') {
    return validationError('Method not allowed');
  }

  const url = new URL(req.url);
  const executionId = url.searchParams.get('id');
  if (!executionId) {
    return validationError('executionId is required');
  }

  const [authError, user] = await initAuthenticate(req);
  if (authError) return authError;

  const supabase = initServiceRoleSupabase();
  const facade = new TaskflowSupabaseFacade(supabase);

  const { data: exec, error } = await facade.getExecution(executionId);
  if (error || !exec) {
    return errorResponse(error || 'Execution not found');
  }

  const { error: tfError } = await facade.getUserTaskflow(exec.taskflowId, user.id);
  if (tfError) return errorResponse(tfError);

  const { data, error: traceError } = await facade.getExecutionTrace(executionId);
  if (traceError || !data) return errorResponse(traceError || 'Trace not found');

  return new Response(JSON.stringify(data), {
    headers: { ...corsHeaders, 'Content-Type': 'application/json' },
  });
}

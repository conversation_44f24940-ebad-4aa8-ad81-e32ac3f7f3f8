import { supabase } from '../lib/supabase';
import fs from 'fs/promises';
import { getRunner } from '../../../ma-next/netlify/functions/_tools/actions';
import { getPseudoNangoAction } from '../../../ma-next/netlify/functions/_nango/getPseudoNangoAction';
import { ACTION_OUTPUT_MODELS_ZOD, ACTION_OUTPUTS } from '../../../ma-next/netlify/functions/_agents/nangoConstants';

async function getConnectionId(providerKey: string): Promise<string | null> {
  const { data } = await supabase
    .from('connections')
    .select('id')
    .eq('providerKey', providerKey)
    .limit(1)
    .maybeSingle();
  return data?.id || null;
}

export async function executeProviderActions(
  providerKey: string,
  steps: { actionKey: string; params: Record<string, any>; filename?: string }[],
  onProgress?: (result: { action: string; input: any; output?: any; valid?: boolean; error?: string }) => void
) {
  const connectionId = await getConnectionId(providerKey);
  if (!connectionId) throw new Error(`No ${providerKey} connection found`);

  await fs.mkdir(`result-data/scripts/${providerKey}`, { recursive: true });
  const nango = getPseudoNangoAction(providerKey, connectionId);
  const results: any[] = [];

  for (const step of steps) {
    try {
      // Notify start of step
      if (onProgress) {
        onProgress({ action: step.actionKey, input: step.params });
      }

      const runner = getRunner(providerKey, step.actionKey);
      if (!runner) throw new Error(`No handler for ${providerKey}:${step.actionKey}`);

      const output = await runner(nango, step.params);
      const modelName = ACTION_OUTPUTS.find(
        (o) => o.provider === providerKey && o.action === step.actionKey
      )?.model;
      const valid = modelName
        ? ACTION_OUTPUT_MODELS_ZOD[modelName as keyof typeof ACTION_OUTPUT_MODELS_ZOD]?.safeParse(output).success ?? true
        : true;

      const file = `result-data/scripts/${providerKey}/${step.filename || step.actionKey}.json`;
      await fs.writeFile(file, JSON.stringify({ input: step.params, output }, null, 2));

      const result = { action: step.actionKey, input: step.params, output, valid };
      results.push(result);

      // Notify completion of step
      if (onProgress) {
        onProgress(result);
      }
    } catch (error: any) {
      const errorResult = { action: step.actionKey, input: step.params, error: error.message };
      results.push(errorResult);

      // Notify error
      if (onProgress) {
        onProgress(errorResult);
      }
    }
  }

  return results;
}

export async function saveScriptResult(providerKey: string, results: any[]): Promise<void> {
  await fs.writeFile(
    `result-data/scripts/${providerKey}.json`,
    JSON.stringify({ results }, null, 2)
  );
}

import { executeProviderActions, saveScriptResult } from './providerRunner';

export async function runGoogleSheetTest(onProgress?: (result: any) => void) {
  const results = await executeProviderActions(
    'google-sheet',
    [
      {
        actionKey: 'create-sheet',
        params: {
          title: 'ActionsInspector Test Sheet',
          data: [
            ['Name', 'Email', 'Role'],
            ['<PERSON>', '<EMAIL>', 'Developer'],
            ['<PERSON>', '<EMAIL>', 'Designer'],
          ],
        },
      },
    ],
    onProgress
  );

  const spreadsheetId = (results[0].output as any)?.id;

  if (spreadsheetId) {
    const more = await executeProviderActions(
      'google-sheet',
      [{ actionKey: 'fetch-spreadsheet', params: { spreadsheetId } }],
      onProgress
    );
    results.push(...more);

    const editResult = await executeProviderActions(
      'google-sheet',
      [
        {
          actionKey: 'edit-sheet',
          params: {
            spreadsheetId,
            updates: [
              {
                data: {
                  rows: [
                    {
                      cells: ['', '', '', ''],
                    },
                  ],
                },
                range: 'A1:D1',
              },
            ],
          },
        },
      ],
      onProgress
    );
    results.push(...editResult);

    const appendResult = await executeProviderActions(
      'google-sheet',
      [
        {
          actionKey: 'append-sheet',
          params: {
            spreadsheetId,
            range: 'A:C',
            values: [['Alice Brown', '<EMAIL>', 'QA Engineer']],
          },
        },
      ],
      onProgress
    );
    results.push(...appendResult);
  }
  await saveScriptResult('google-sheet', results);
  return results;
}

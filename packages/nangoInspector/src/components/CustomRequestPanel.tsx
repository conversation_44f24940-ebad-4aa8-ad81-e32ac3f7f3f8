import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info } from 'lucide-react';
import { ConnectionInspector } from './ConnectionInspector';

interface CustomRequestPanelProps {
  provider: string;
  onSend: (
    provider: string,
    endpoint: string,
    method: string,
    body: any,
    connectionId?: string
  ) => Promise<void>;
  isSending: boolean;
  lastResult: any;
}

export function CustomRequestPanel({
  provider,
  onSend,
  isSending,
  lastResult,
}: CustomRequestPanelProps) {
  const [endpoint, setEndpoint] = useState('');
  const [method, setMethod] = useState('GET');
  const [bodyText, setBodyText] = useState('');
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | undefined>();

  // Check if endpoint is a fully qualified URL
  const isFullyQualifiedUrl = /^https?:\/\//.test(endpoint);

  const handleSend = async () => {
    let body: any = undefined;
    if (bodyText.trim()) {
      try {
        body = JSON.parse(bodyText);
      } catch {
        alert('Invalid JSON body');
        return;
      }
    }
    await onSend(provider, endpoint, method, body, selectedConnectionId);
  };

  return (
    <div className="space-y-6">
      <ConnectionInspector
        providerKey={provider}
        selectedConnectionId={selectedConnectionId}
        onConnectionSelect={setSelectedConnectionId}
        syncKey="custom-request"
      />
      <Card>
        <CardHeader>
          <CardTitle>Custom Request</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Endpoint</label>
            <Input
              value={endpoint}
              onChange={e => setEndpoint(e.target.value)}
              placeholder="/path"
            />
          </div>

          {isFullyQualifiedUrl && (
            <Alert variant="info">
              <Info className="h-4 w-4" />
              <AlertDescription>
                Using a fully qualified URL will override the base URL and may not represent a true
                test of your integration's configuration.
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <label className="text-sm font-medium">Method</label>
            <Select value={method} onValueChange={setMethod}>
              <SelectTrigger>
                <SelectValue placeholder="GET" />
              </SelectTrigger>
              <SelectContent>
                {['GET', 'POST', 'PUT', 'PATCH', 'DELETE'].map(m => (
                  <SelectItem key={m} value={m}>
                    {m}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Body (JSON)</label>
            <Textarea
              value={bodyText}
              onChange={e => setBodyText(e.target.value)}
              className="font-mono text-xs"
            />
          </div>
          <Button onClick={handleSend} disabled={isSending} className="w-full">
            {isSending ? 'Sending...' : 'Send Request'}
          </Button>
        </CardContent>
      </Card>
      {lastResult && (
        <Card>
          <CardHeader>
            <CardTitle>Response</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-[1600px]">
              {JSON.stringify(lastResult, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

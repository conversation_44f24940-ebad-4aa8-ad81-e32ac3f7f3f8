import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON>ting<PERSON>, ChevronDown, ChevronUp } from 'lucide-react';
import { ProviderSummary } from '@/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import DropdownMenu from './DropdownMenu';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { cn } from '@/lib/utils';
import {
  DropboxIcon,
  GithubIcon,
  GmailIcon,
  GoogleCalendarIcon,
  GoogleDocsIcon,
  GoogleDriveIcon,
  GoogleSheetsIcon,
  HarvestIcon,
  LinearIcon,
  LinkedInIcon,
  NotionIcon,
  SlackIcon,
  TwitterIcon,
} from '../../../ma-next/src/components/icons/providers';

// Icon mapping for stable rendering
const PROVIDER_ICONS = {
  slack: SlackIcon,
  github: GithubIcon,
  'google-calendar': GoogleCalendarIcon,
  'google-mail': GmailIcon,
  'google-docs': GoogleDocsIcon,
  'google-drive': GoogleDriveIcon,
  'google-sheet': GoogleSheetsIcon,
  notion: NotionIcon,
  linear: LinearIcon,
  harvest: HarvestIcon,
  linkedin: LinkedInIcon,
  'twitter-v2': TwitterIcon,
  'x-social': TwitterIcon,
  dropbox: DropboxIcon,
} as const;

const ProviderIcon = ({ providerName }: { providerName: string }) => {
  const IconComponent = PROVIDER_ICONS[providerName as keyof typeof PROVIDER_ICONS] || Settings;
  return <IconComponent className="w-5 h-5" />;
};

interface ProviderListProps {
  providers: ProviderSummary[];
  selected: { provider: string; key: string; type: 'action' | 'sync' | 'provider-script' | 'custom' } | null;
  onActionSelect: (provider: string, action: string) => void;
  onSyncSelect: (provider: string, sync: string) => void;
  onProviderScriptSelect: (provider: string) => void;
  onCustomSelect: (provider: string) => void;
  onRunAction: (provider: string, action: string) => void;
  onRunSync: (provider: string, sync: string) => void;
  onRunProviderScript: (provider: string) => void;
}

export function ProviderList({
  providers,
  selected,
  onActionSelect,
  onSyncSelect,
  onProviderScriptSelect,
  onCustomSelect,
  onRunAction,
  onRunSync,
  onRunProviderScript,
}: ProviderListProps) {
  const [expandedProviders, setExpandedProviders] = useState<string[]>([]);
  const [syncsOnly, setSyncsOnly] = useState(false);

  // Hydrate from localStorage after mounting to avoid SSR mismatch
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('ni-expanded-providers');
      if (stored) {
        try {
          setExpandedProviders(JSON.parse(stored));
        } catch {}
      }
      const syncPref = localStorage.getItem('ni-syncs-only');
      if (syncPref === 'true') setSyncsOnly(true);
    }
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ni-expanded-providers', JSON.stringify(expandedProviders));
    }
  }, [expandedProviders]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ni-syncs-only', syncsOnly ? 'true' : 'false');
    }
  }, [syncsOnly]);

  const allProviderNames = providers.map(p => p.name);
  const areAllExpanded =
    allProviderNames.length > 0 && allProviderNames.every(name => expandedProviders.includes(name));

  const toggleExpandAll = () => {
    if (areAllExpanded) {
      // Collapse all
      setExpandedProviders([]);
    } else {
      // Expand all
      setExpandedProviders(allProviderNames);
      // Auto-select first available item from first provider if nothing is selected
      if (!selected && providers.length > 0) {
        const firstProvider = providers[0];
        if (firstProvider.syncs.length > 0) {
          onSyncSelect(firstProvider.name, firstProvider.syncs[0].sync);
        } else if (!syncsOnly && hasProviderScript(firstProvider.name)) {
          onProviderScriptSelect(firstProvider.name);
        } else if (!syncsOnly && firstProvider.actions.length > 0) {
          onActionSelect(firstProvider.name, firstProvider.actions[0].action);
        }
      }
    }
  };

  const handleExpandedChange = (val: string[]) => {
    setExpandedProviders(prev => {
      const newProvider = val.find(p => !prev.includes(p));
      if (newProvider) {
        const providerData = providers.find(p => p.name === newProvider);
        if (providerData) {
          if (providerData.syncs.length > 0) {
            onSyncSelect(newProvider, providerData.syncs[0].sync);
          } else if (!syncsOnly && hasProviderScript(newProvider)) {
            onProviderScriptSelect(newProvider);
          } else if (!syncsOnly && providerData.actions.length > 0) {
            onActionSelect(newProvider, providerData.actions[0].action);
          }
        }
      }
      return val;
    });
  };

  const isActionSelected = (provider: string, action: string) => {
    return (
      selected?.type === 'action' && selected?.provider === provider && selected.key === action
    );
  };

  const isProviderScriptSelected = (provider: string) => {
    return selected?.type === 'provider-script' && selected?.provider === provider;
  };

  const isSyncSelected = (provider: string, sync: string) => {
    return selected?.type === 'sync' && selected.provider === provider && selected.key === sync;
  };

  const isCustomSelected = (provider: string) => {
    return selected?.type === 'custom' && selected.provider === provider;
  };

  const hasProviderScript = (provider: string) => {
    // Only show provider script for providers that have test scripts
    return [
      'slack',
      'google-drive',
      'dropbox',
      'github',
      'google-calendar',
      'google-docs',
      'google-mail',
      'google-sheet',
      'harvest',
      'linear',
      'linkedin',
      'notion',
      'x-social',
      'twitter-v2',
    ].includes(provider);
  };

  return (
    <Card className="h-full max-h-[calc(100vh-12rem)] overflow-y-auto">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center space-x-2">
            <DropdownMenu
              trigger={<Settings className="h-5 w-5" />}
              items={[{
                label: syncsOnly ? 'Show All' : 'Show Only Syncs',
                onClick: () => setSyncsOnly(prev => !prev)
              }]}
            />
            <span>Providers</span>
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleExpandAll}
            className="flex items-center space-x-1 text-xs"
          >
            {areAllExpanded ? (
              <>
                <ChevronUp className="h-3 w-3" />
                <span>Collapse All</span>
              </>
            ) : (
              <>
                <ChevronDown className="h-3 w-3" />
                <span>Expand All</span>
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Accordion
          type="multiple"
          value={expandedProviders}
          onValueChange={handleExpandedChange}
          className="w-full"
        >
          {providers.map(provider => (
            <AccordionItem key={provider.name} value={provider.name} className="border-b-0">
              <AccordionTrigger className="px-6 py-3 hover:bg-muted/50">
                <div className="flex items-center justify-between w-full">
                  <div className="flex items-center space-x-3">
                    <span>
                      <ProviderIcon providerName={provider.name} />
                    </span>
                    <div className="text-left">
                      <div className="font-medium">{provider.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {provider.actions.length} action{provider.actions.length !== 1 ? 's' : ''} • {provider.syncs.length} sync{provider.syncs.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  {!syncsOnly && hasProviderScript(provider.name) && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-8 w-8 p-0"
                      onClick={e => {
                        e.stopPropagation();
                        onRunProviderScript(provider.name);
                      }}
                    >
                      <Play className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent className="pb-0">
                <div className="space-y-1">
                  {/* Provider Script Option */}
                  {!syncsOnly && hasProviderScript(provider.name) && (
                    <>
                      <div className="mt-2 ml-5 text-xs font-semibold text-muted-foreground">
                        Script
                      </div>

                      <div
                        className={cn(
                          'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent',
                          isProviderScriptSelected(provider.name) && 'bg-muted border-l-primary'
                        )}
                        onClick={() => onProviderScriptSelect(provider.name)}
                      >
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium text-blue-600 dark:text-blue-400 ml-2">
                            🚀 Chained script for {provider.name}
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="ml-2 h-8 w-8 p-0"
                          onClick={e => {
                            e.stopPropagation();
                            onRunProviderScript(provider.name);
                          }}
                        >
                          <Play className="h-3 w-3" />
                        </Button>
                      </div>
                    </>
                  )}

                  {/* Syncs */}
                  {provider.syncs.length > 0 && (
                    <div className="mt-2 ml-5 text-xs font-semibold text-muted-foreground">
                      Syncs
                    </div>
                  )}
                  {provider.syncs.map(sync => (
                    <div
                      key={`sync-${sync.sync}`}
                      className={cn(
                        'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent ml-5',
                        isSyncSelected(provider.name, sync.sync) && 'bg-muted border-l-primary'
                      )}
                      onClick={() => onSyncSelect(provider.name, sync.sync)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{sync.sync}</div>
                        {sync.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {sync.description}
                          </div>
                        )}
                        {sync.model && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 truncate">
                            → {sync.model}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-8 w-8 p-0"
                        onClick={e => {
                          e.stopPropagation();
                          onRunSync(provider.name, sync.sync);
                        }}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}

                  {!syncsOnly && provider.actions.length > 0 && (
                    <div className="mt-2 ml-5 text-xs font-semibold text-muted-foreground">
                      Actions
                    </div>
                  )}
                  {/* Individual Actions */}
                  {!syncsOnly && provider.actions.map(action => (
                    <div
                      key={action.action}
                      className={cn(
                        'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent ml-5',
                        isActionSelected(provider.name, action.action) &&
                          'bg-muted border-l-primary'
                      )}
                      onClick={() => onActionSelect(provider.name, action.action)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">{action.action}</div>
                        {action.description && (
                          <div className="text-xs text-muted-foreground truncate">
                            {action.description}
                          </div>
                        )}
                        {action.model && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 truncate">
                            → {action.model}
                          </div>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="ml-2 h-8 w-8 p-0"
                        onClick={e => {
                          e.stopPropagation();
                          onRunAction(provider.name, action.action);
                        }}
                      >
                        <Play className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}

                  {!syncsOnly && (
                    <>
                      <div className="mt-2 ml-5 text-xs font-semibold text-muted-foreground">
                        Custom
                      </div>
                      <div
                        className={cn(
                          'flex items-center justify-between px-6 py-2 hover:bg-muted/50 cursor-pointer border-l-2 border-transparent ml-5',
                          isCustomSelected(provider.name) && 'bg-muted border-l-primary'
                        )}
                        onClick={() => onCustomSelect(provider.name)}
                      >
                        <div className="flex-1 min-w-0">
                          <div className="text-sm font-medium truncate">Custom Request</div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </CardContent>
    </Card>
  );
}

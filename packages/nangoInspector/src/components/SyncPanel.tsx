import { useState, useEffect } from 'react';
import {
  Play,
  Database,
  Code,
  Loader2,
  <PERSON><PERSON>,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  SortAsc,
  SortDesc,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SyncDefinition } from '@/types';
import { ConnectionInspector } from './ConnectionInspector';
import { RichSyncOutputs } from '../../../ma-next/src/components/rich-ui/RichSyncOutputs';
import { extractDatePaths, sortRecordsByPath } from '@/lib/dateUtils';

interface SyncPanelProps {
  sync: SyncDefinition | null;
  onListRecords: (
    provider: string,
    model: string,
    modifiedAfter?: string,
    connectionId?: string,
    condition?: any
  ) => Promise<void>;
  isListing: boolean;
  records: any[] | null;
  rawApiResponse?: any; // Full API response with records, next_cursor, etc.
}

export function SyncPanel({
  sync,
  onListRecords,
  isListing,
  records,
  rawApiResponse,
}: SyncPanelProps) {
  const [modifiedAfter, setModifiedAfter] = useState('');
  const [conditionText, setConditionText] = useState('');
  const [isValidCondition, setIsValidCondition] = useState(true);
  const [sortField, setSortField] = useState('default');
  const [sortOptions, setSortOptions] = useState<string[]>([]);
  const [sortedRecords, setSortedRecords] = useState<any[] | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [displayCount, setDisplayCount] = useState<number | 'all'>(3);
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | undefined>();
  const [showRaw, setShowRaw] = useState(false);

  // Reset connection selection when sync changes
  useEffect(() => {
    setSelectedConnectionId(undefined);
  }, [sync]);

  // Load stored condition on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('ni-condition');
      if (stored) {
        setConditionText(stored);
        try {
          JSON.parse(stored);
          setIsValidCondition(true);
        } catch {
          setIsValidCondition(false);
        }
      }
    }
  }, []);

  // Persist condition to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ni-condition', conditionText);
    }
  }, [conditionText]);

  // Update sort options when records change
  useEffect(() => {
    if (Array.isArray(records)) {
      setSortOptions(extractDatePaths(records));
    } else {
      setSortOptions([]);
    }
  }, [records]);

  // Sort records when sort field/order changes or records update
  useEffect(() => {
    if (Array.isArray(records)) {
      let r = records;
      if (sortField !== 'default') {
        // Apply field-based sorting
        r = sortRecordsByPath(records, sortField, sortOrder);
      } else {
        // Default sorting - natural or reverse
        if (sortOrder === 'desc') {
          r = [...records].reverse();
        }
        // For 'asc' with default, keep natural order (no change needed)
      }
      setSortedRecords(r);
    } else {
      setSortedRecords(records);
    }
  }, [records, sortField, sortOrder]);

  if (!sync) {
    return (
      <Card className="h-full">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center h-full">
          <Code className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Select a Sync</h3>
          <p className="text-muted-foreground">
            Choose a sync from the provider list to view its records.
          </p>
        </CardContent>
      </Card>
    );
  }

  const handleConditionTextChange = (text: string) => {
    setConditionText(text);
    try {
      JSON.parse(text);
      setIsValidCondition(true);
    } catch {
      setIsValidCondition(false);
    }
  };

  const handleList = async () => {
    let condition: any = undefined;
    if (conditionText.trim()) {
      try {
        condition = JSON.parse(conditionText);
        setConditionText(JSON.stringify(condition, null, 2));
        setIsValidCondition(true);
      } catch (e) {
        setIsValidCondition(false);
        alert('Invalid JSON condition');
        return;
      }
    }
    await onListRecords(
      sync.provider,
      sync.sync,
      modifiedAfter || undefined,
      selectedConnectionId,
      condition
    );
  };

  const handleCopy = () => {
    let data;
    if (showRaw) {
      // When in raw mode, copy the full API response
      data = rawApiResponse || records;
    } else {
      // When in processed mode, copy the sorted/filtered records
      data = sortedRecords ?? records;
      // Respect display count for copying processed data
      if (Array.isArray(data) && displayCount !== 'all') {
        data = data.slice(0, typeof displayCount === 'number' ? displayCount : 10);
      }
    }

    if (!data) return;
    navigator.clipboard.writeText(JSON.stringify(data, null, 2));
  };

  const setRelativeTime = (hours: number) => {
    const now = new Date();
    const targetTime = new Date(now.getTime() - hours * 60 * 60 * 1000);
    setModifiedAfter(targetTime.toISOString());
  };

  return (
    <div className="space-y-6">
      {/* Sync Connection */}
      <ConnectionInspector
        providerKey={sync.provider}
        selectedConnectionId={selectedConnectionId}
        onConnectionSelect={setSelectedConnectionId}
        syncKey={sync.sync}
        outputModel={sync.model || undefined}
      />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>Records - {sync.sync}</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">{sync.description}</p>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium">Modified After (ISO)</label>
              <div className="flex space-x-1">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setRelativeTime(48)}
                  className="h-7 px-2 text-xs"
                >
                  -48h
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setRelativeTime(24)}
                  className="h-7 px-2 text-xs"
                >
                  -24h
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setRelativeTime(1)}
                  className="h-7 px-2 text-xs"
                >
                  -1h
                </Button>
              </div>
            </div>
            <Input
              value={modifiedAfter}
              onChange={e => setModifiedAfter(e.target.value)}
              placeholder="2024-01-01T00:00:00Z"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Condition (JSON Logic)</label>
            <Textarea
              value={conditionText}
              onChange={e => handleConditionTextChange(e.target.value)}
              placeholder='{"==":[{"var":"field"},"value"]}'
              className={`font-mono text-xs ${!isValidCondition ? 'border-red-500 ring-1 ring-red-500' : ''}`}
            />
            {!isValidCondition && <p className="text-xs text-red-500">Invalid JSON format</p>}
          </div>
          <Button onClick={handleList} disabled={isListing} className="w-full">
            {isListing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Listing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                List Records
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {sortedRecords && (
        <>
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:items-center lg:justify-between">
                {/* Sort Controls */}
                <div className="flex flex-col space-y-2 lg:flex-row lg:space-y-0 lg:items-center lg:space-x-4">
                  <span className="text-sm font-medium text-muted-foreground">Sort by:</span>
                  <div className="flex items-center space-x-2">
                    <Select
                      value={sortField}
                      onValueChange={value => {
                        setSortField(value);
                        // Set default sort order based on field type
                        if (value === 'default') {
                          setSortOrder('asc'); // Natural order for default
                        } else {
                          setSortOrder('desc'); // Descending for field-based sorting
                        }
                      }}
                      disabled={sortOptions.length === 0}
                    >
                      <SelectTrigger className="h-9 w-36 text-sm">
                        <SelectValue placeholder="Default" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">Default</SelectItem>
                        {sortOptions.map(opt => (
                          <SelectItem key={opt} value={opt}>
                            {opt}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      className="h-9 px-3"
                    >
                      {sortField !== 'default' ? (
                        sortOrder === 'asc' ? (
                          <>
                            <ArrowUp className="h-3 w-3 mr-1" />
                            Asc
                          </>
                        ) : (
                          <>
                            <ArrowDown className="h-3 w-3 mr-1" />
                            Desc
                          </>
                        )
                      ) : sortOrder === 'asc' ? (
                        'Natural'
                      ) : (
                        'Reverse'
                      )}
                    </Button>
                  </div>
                </div>

                {/* Display Count Controls */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-muted-foreground">Show:</span>
                  <div className="flex space-x-1">
                    <Button
                      size="sm"
                      variant={displayCount === 3 ? 'default' : 'outline'}
                      onClick={() => setDisplayCount(3)}
                      className="h-8 px-3 text-sm"
                    >
                      3
                    </Button>
                    <Button
                      size="sm"
                      variant={displayCount === 10 ? 'default' : 'outline'}
                      onClick={() => setDisplayCount(10)}
                      className="h-8 px-3 text-sm"
                    >
                      10
                    </Button>
                    <Button
                      size="sm"
                      variant={displayCount === 'all' ? 'default' : 'outline'}
                      onClick={() => setDisplayCount('all')}
                      className="h-8 px-3 text-sm"
                    >
                      All
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Try rich display first */}
          {RichSyncOutputs.canDisplay(sync.provider, sync.sync) &&
            Array.isArray(sortedRecords) &&
            (displayCount === 'all'
              ? sortedRecords
              : sortedRecords.slice(0, typeof displayCount === 'number' ? displayCount : 10)
            )?.map(record => (
              <div key={record.id}>
                <RichSyncOutputs providerKey={sync.provider} syncKey={sync.sync} output={record} />
              </div>
            ))}

          {/* Fallback to detailed records display */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Records</span>
                <div className="flex items-center space-x-4">
                  <Button
                    size="sm"
                    variant={showRaw ? 'default' : 'outline'}
                    onClick={() => setShowRaw(!showRaw)}
                    className="h-7 px-2 text-xs"
                    title={showRaw ? 'Show processed records' : 'Show raw server response'}
                  >
                    {showRaw ? 'Raw' : 'Processed'}
                  </Button>
                  <span className="text-sm font-normal text-muted-foreground">
                    {Array.isArray(sortedRecords)
                      ? displayCount === 'all' || showRaw
                        ? `${sortedRecords.length} record${sortedRecords.length !== 1 ? 's' : ''}`
                        : `${Math.min(typeof displayCount === 'number' ? displayCount : 10, sortedRecords.length)} of ${sortedRecords.length} record${sortedRecords.length !== 1 ? 's' : ''}`
                      : 'Result'}
                  </span>
                  <button
                    onClick={handleCopy}
                    className="text-muted-foreground hover:text-primary"
                    title={showRaw ? 'Copy raw API response' : 'Copy processed records'}
                  >
                    <Copy className="h-4 w-4" />
                  </button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-[1600px]">
                {JSON.stringify(
                  showRaw
                    ? rawApiResponse || records // Show full raw API response including next_cursor, etc.
                    : Array.isArray(sortedRecords) && displayCount !== 'all'
                      ? sortedRecords.slice(0, typeof displayCount === 'number' ? displayCount : 10)
                      : sortedRecords, // Show processed/sorted records
                  null,
                  2
                )}
              </pre>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

import type { NextApiRequest, NextApiResponse } from 'next';
import { Nango } from '@nangohq/node';
import { supabase } from '../../lib/supabase';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  const {
    provider,
    endpoint,
    method,
    body,
    connectionId: reqConn,
  } = req.body as {
    provider: string;
    endpoint: string;
    method?: string;
    body?: any;
    connectionId?: string;
  };

  if (!provider || !endpoint) {
    return res.status(400).json({ success: false, error: 'provider and endpoint are required' });
  }

  const connectionId = reqConn || (await getConnectionId(provider));
  if (!connectionId) {
    return res.status(400).json({ success: false, error: `No ${provider} connection found` });
  }

  if (!process.env.NANGO_SECRET_KEY) {
    return res.status(500).json({ success: false, error: 'NANGO_SECRET_KEY not set' });
  }

  const nango = new Nango({ secretKey: process.env.NANGO_SECRET_KEY });

  try {
    // Check if endpoint is a fully qualified URL (contains protocol)
    const isFullyQualifiedUrl = /^https?:\/\//.test(endpoint);

    const proxyConfig: any = {
      connectionId,
      providerConfigKey: provider,
      method: (method || 'GET') as any,
      data: body,
    };

    if (isFullyQualifiedUrl) {
      // Extract base URL and path from fully qualified URL
      const url = new URL(endpoint);
      proxyConfig.baseUrlOverride = `${url.protocol}//${url.host}/`;
      proxyConfig.endpoint = url.pathname + url.search;
    } else {
      proxyConfig.endpoint = endpoint;
    }

    const result = await nango.proxy(proxyConfig);
    res.status(200).json({ success: true, result: result.data ?? result });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message || 'Internal error' });
  }
}

async function getConnectionId(provider: string): Promise<string | null> {
  const { data } = await supabase
    .from('connections')
    .select('id')
    .eq('providerKey', provider)
    .limit(1)
    .maybeSingle();
  return data?.id || null;
}

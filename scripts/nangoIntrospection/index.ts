import fsExtra from 'fs-extra';
import { fetchData } from './fetchData';
import { transformToMakeAgentFormat } from './transformToMakeAgentFormat';
import {
  generateActionInputModelsJsonSchema,
  generateActionInputModelsZod,
  generateActionInputs,
  generateActionInputModelsKeyed,
  generateActionInputsKeyed,
  generateActionInputsString,
  generateActionOutputModelsJsonSchema,
  generateActionOutputModelsZod,
  generateActionOutputs,
  generateActionOutputsString,
  generateSyncOutputModelsJsonSchema,
  generateSyncOutputModelsZod,
  generateActionOutputModelsKeyed,
  generateSyncOutputs,
  generateSyncOutputsString,
  generateActionOutputsKeyed,
  generateSyncOutputsKeyed,
  generateSyncOutputModelsKeyed,
} from './formatTransformers';
import { MakeAgentFormat } from './types';

// Parse command-line arguments for --force flag
const args = process.argv.slice(2);
const forceFetch = args.includes('--force') || args.includes('-f');

async function generateMakeAgentFormat() {
  try {
    // Fetch data and generate makeagent-format.json
    const { nangoData, openAIData } = await fetchData({ force: forceFetch });
    const makeAgentFormat = transformToMakeAgentFormat(nangoData, openAIData);
    const makeAgentFilePath = new URL('./makeagent-format.json', import.meta.url).pathname;
    await fsExtra.outputFile(makeAgentFilePath, JSON.stringify(makeAgentFormat, null, 2), 'utf8');
    console.log(`Saved transformed data to makeagent-format ${makeAgentFilePath}`);

    // Read makeagent-format.json
    const makeAgentData: MakeAgentFormat = fsExtra.readJSONSync(makeAgentFilePath);

    // Generate output formats - organized by inputs, outputs, syncs
    const generatedConstants: Record<string, string> = {
      // Action Inputs
      ACTION_INPUTS_STRING: generateActionInputsString(makeAgentData),
      ACTION_INPUTS: generateActionInputs(makeAgentData),
      ACTION_INPUTS_KEYED: generateActionInputsKeyed(makeAgentData),
      ACTION_INPUT_MODELS_ZOD: generateActionInputModelsZod(makeAgentData),
      ACTION_INPUT_MODELS_JSON_SCHEMA: generateActionInputModelsJsonSchema(makeAgentData),
      ACTION_INPUT_MODELS_KEYED: generateActionInputModelsKeyed(makeAgentData),
      
      // Action Outputs
      ACTION_OUTPUTS_STRING: generateActionOutputsString(makeAgentData),
      ACTION_OUTPUTS: generateActionOutputs(makeAgentData),
      ACTION_OUTPUTS_KEYED: generateActionOutputsKeyed(makeAgentData),
      ACTION_OUTPUT_MODELS_ZOD: generateActionOutputModelsZod(makeAgentData),
      ACTION_OUTPUT_MODELS_JSON_SCHEMA: generateActionOutputModelsJsonSchema(makeAgentData),
      ACTION_OUTPUT_MODELS_KEYED: generateActionOutputModelsKeyed(makeAgentData),
      
      // Sync Outputs
      SYNC_OUTPUTS_STRING: generateSyncOutputsString(makeAgentData),
      SYNC_OUTPUTS: generateSyncOutputs(makeAgentData),
      SYNC_OUTPUTS_KEYED: generateSyncOutputsKeyed(makeAgentData),
      SYNC_OUTPUT_MODELS_ZOD: generateSyncOutputModelsZod(makeAgentData),
      SYNC_OUTPUT_MODELS_JSON_SCHEMA: generateSyncOutputModelsJsonSchema(makeAgentData),
      SYNC_OUTPUT_MODELS_KEYED: generateSyncOutputModelsKeyed(makeAgentData),
    };

    // Define target file configurations
    const targetFileConfig: Record<string, string[]> = {
      [new URL('../../packages/mastra/src/mastra/nangoConstants.ts', import.meta.url).pathname]: [
        'SYNC_OUTPUTS_STRING',
        'ACTION_INPUTS_STRING',
        'ACTION_OUTPUTS_STRING',
        'ACTION_INPUT_MODELS_ZOD',
        'ACTION_OUTPUT_MODELS_ZOD',
        'SYNC_OUTPUT_MODELS_ZOD',
        'ACTION_INPUT_MODELS_JSON_SCHEMA',
        'ACTION_OUTPUT_MODELS_JSON_SCHEMA',
        'SYNC_OUTPUT_MODELS_JSON_SCHEMA',
      ],
      [new URL(
        '../../packages/ma-next/netlify/functions/_agents/nangoConstants.ts',
        import.meta.url
      ).pathname]: [
        'SYNC_OUTPUTS_STRING',
        'ACTION_INPUTS_STRING',
        'ACTION_OUTPUTS_STRING',
        'SYNC_OUTPUTS',
        'ACTION_INPUTS',
        'ACTION_OUTPUTS',
        'ACTION_INPUT_MODELS_ZOD',
        'ACTION_OUTPUT_MODELS_ZOD',
        'SYNC_OUTPUT_MODELS_ZOD',
        'ACTION_INPUT_MODELS_JSON_SCHEMA',
        'ACTION_OUTPUT_MODELS_JSON_SCHEMA',
        'SYNC_OUTPUT_MODELS_JSON_SCHEMA',
        'ACTION_INPUT_MODELS_KEYED',
        'ACTION_OUTPUT_MODELS_KEYED',
        'SYNC_OUTPUT_MODELS_KEYED',
        'ACTION_INPUTS_KEYED',
        'ACTION_OUTPUTS_KEYED',
        'SYNC_OUTPUTS_KEYED',
      ],
      [new URL("../../packages/ma-next/src/config/nangoConstants.ts", import.meta.url).pathname]:
        [
          "ACTION_INPUTS_KEYED",
          "ACTION_OUTPUTS_KEYED",
          "SYNC_OUTPUTS_KEYED",
        ],
    };

    // Header and import statements
    const headerComment =
      '// Auto-generated by scripts/nangoIntrospection.ts on ' +
      new Date().toISOString() +
      '\n// LLM, AGENTS, ___NEVER____ _____EVER_____ UPDATE THIS FILE UNDER ANY CIRCUMSTANCES\n\n';
    const supabaseZodImport = `import { z } from "https://esm.sh/zod@latest";\n\n`;
    const zodImport = "import { z } from 'zod';\n\n";

    // Write to target files
    for (const [filePath, requiredConstants] of Object.entries(targetFileConfig)) {
      const needsZodImport = requiredConstants.some(key =>
        ['ACTION_INPUT_MODELS_ZOD', 'ACTION_OUTPUT_MODELS_ZOD', 'SYNC_OUTPUT_MODELS_ZOD'].includes(
          key
        )
      );

      let fileContent = headerComment;
      if (needsZodImport) {
        fileContent += filePath.includes('supabase') ? supabaseZodImport : zodImport;
      }

      for (const constantKey of requiredConstants) {
        if (generatedConstants[constantKey]) {
          fileContent += generatedConstants[constantKey] + '\n';
        } else {
          console.warn(`Warning: Constant key "${constantKey}" not found for file ${filePath}`);
        }
      }

      await fsExtra.outputFile(filePath, fileContent.trim() + '\n', 'utf8');
      console.log(`Successfully generated ${filePath}`);

      await fsExtra.copyFile(
        new URL('../../packages/nango-integrations/models.ts', import.meta.url).pathname,
        new URL('../../packages/ma-next/src/config/nangoModels.ts', import.meta.url).pathname
      );
    }
  } catch (error) {
    console.error('Error generating MakeAgent format and outputs:', error);
  }
}

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  generateMakeAgentFormat().catch(console.error);
}

export { generateMakeAgentFormat };

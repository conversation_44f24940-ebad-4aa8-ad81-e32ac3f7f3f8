{"add-historical-time-entry": {}, "create-client": {}, "create-project": {}, "delete-project": {}, "delete-time-entry": {}, "get-client": {}, "get-project": {}, "get-time-entry": {}, "list-clients": {}, "list-projects": {}, "list-project-tasks": {}, "list-tasks": {}, "list-time-entries": {}, "restart-timer": {}, "start-timer": {}, "stop-timer": {}, "update-time-entry": {}, "issues": {}, "list-files": {}, "add-pull-request-review-comment": {}, "create-issue": {}, "create-organization-repository": {}, "create-pull-request": {}, "create-pull-request-review": {}, "create-repository": {}, "delete-repository": {}, "get-issue": {}, "get-pull-request": {}, "get-pull-request-comments": {}, "get-pull-request-files": {}, "get-pull-request-status": {}, "get-repository": {}, "list-branches": {}, "list-issues": {}, "list-pull-requests": {}, "list-repos": {}, "list-repositories": {}, "merge-pull-request": {}, "update-issue": {}, "update-pull-request": {}, "update-pull-request-branch": {}, "update-repository": {}, "write-file": {}, "messages": {}, "add-reaction-as-user": {}, "create-channel": {}, "delete-message-as-user": {}, "get-channel-history": {}, "get-message-permalink": {}, "get-user-info": {}, "list-channels": {}, "search-messages": {}, "send-message-as-user": {}, "update-message-as-user": {}, "append-sheet": {}, "create-sheet": {}, "edit-sheet": {}, "fetch-spreadsheet": {}, "calendars": {}, "events": {}, "events-fork": {}, "create-event": {"summary": "Event title / name.", "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML.", "location": "Free form text.", "start": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.", "end": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.", "timeZone": "An IANA Time Zone e.g. (Area/City)", "attendees": "A list of attendee email addresses."}, "delete-event": {"calendarId": "Calendar identifier. Use \"primary\" unless otherwise advised.", "eventId": "Event identifier.", "sendUpdates": "Whether to send notifications about the deletion of the event."}, "list-calendars": {}, "list-events": {"calendarId": "Calendar identifier. Use \"primary\" unless otherwise advised.", "timeMin": "Lower bound (inclusive) for an event's end time to filter by. Defaults to now. ISO8601 string format.", "timeMax": "Upper bound (exclusive) for an event's start time to filter by. Defaults to unbounded. ISO8601 string format.", "maxResults": "Defaults to 250. Max 2500.", "pageToken": "Token as per a previous response to get another page of results.", "q": "Free text search terms to find events that match these terms.", "timeZone": "Time zone used in the response. IANA Time Zone e.g. (Area/City). Default is the time zone of the calendar."}, "settings": {}, "update-event": {"calendarId": "Calendar identifier. Use \"primary\" unless otherwise advised.", "eventId": "Event identifier.", "sendUpdates": "Whether to send notifications about the event update.", "summary": "Event title / name.", "description": "Contains e.g. the agenda or specifics of the meeting. Can contain HTML.", "location": "Free form text.", "start": "Starting datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the end parameter.", "end": "Ending datetime (ISO8601 string format) or date (full-day-event - yyyy-mm-dd string format) of the event. Format must match the format of the start parameter.", "timeZone": "An IANA Time Zone e.g. (Area/City)", "attendees": "A list of attendee email addresses."}, "whoami": {}, "emails": {}, "emails-fork": {}, "emails-labels-added": {}, "labels": {}, "compose-draft": {}, "compose-draft-reply": {}, "create-filter": {}, "delete-message": {}, "get-message": {}, "list-messages": {}, "modify-message-labels": {}, "send-email": {}, "trash-message": {}, "untrash-message": {}, "files-fork": {}, "copy-file": {}, "create-folder": {}, "create-user": {}, "delete-file": {}, "fetch-file": {}, "get-file": {}, "move-file": {}, "search-files": {}, "upload-file": {}, "create-database": {}, "create-page": {}, "get-database": {}, "get-page": {}, "query-database": {}, "search": {}, "update-database": {}, "update-page": {}, "create-document": {}, "fetch-document": {}, "get-document": {}, "update-document": {}, "delete-issue": {}, "fetch-models": {}, "get-team": {}, "list-teams": {}, "update-project": {}, "meetings": {}, "recording-files": {}, "users": {}, "create-meeting": {}, "delete-meeting": {}, "delete-user": {}, "documents": {}, "documents-fork": {}, "folders": {}, "fetch-google-doc": {}, "fetch-google-sheet": {}, "folder-content": {}, "list-documents": {}, "list-root-folders": {}, "upload-document": {}, "user-mentions": {}, "user-tweets": {}, "get-user-profile": {}, "send-post": {}, "post": {}}
# MakeAgent Application Architecture

## Overview

MakeAgent is a multi-package monorepo centered around the `ma-next` package, which provides a chat application with two user-facing modes:

- **Task Mode**: An agent that performs actions via third-party integrations.
- **Agent Mode**: An agent that creates "taskflows" (internal term for workflows) utilizing AI nodes to function as agentic systems.

## Core Components

### ma-next

The primary application package, consisting of:

- **Frontend**: A Next.js application with React components.
- **Backend**: Netlify Functions that serve the application and handle server-side logic.
- **Taskflow Engine**: Located in the `_taskflow` folder, orchestrates workflow execution.
- **Agent Definitions**: Located in the `_agents` folder, contains implementations for agents used in task and agent modes, as well as custom agents for workflows.

### Nango (Third-Party Service)

Nango is a third-party service used for authentication and event handling for third-party integrations:

- **Authentication**: Manages OAuth connections to third-party services, configured via the `ma-next` frontend and recorded in the database through the sync webhook.
- **Events Layer**: Processes events from integrated services, sending them to the `ma-next` backend via a sync webhook (`ma-next/netlify/functions/sync/index.mts`).

#### Nango Integration Code

Integration code is contained in `packages/emcpe-nango-integrations`:

- **Actions and Syncs**: Both are defined in `packages/emcpe-nango-integrations/<provider>` (e.g., `google-calendar/actions/create-event.ts` and `google-mail/syncs/emails-fork.ts`).
- **Deployment**:
  - Actions and syncs are combined into `packages/nango-integrations` for deployment to Nango.
  - Actions are copied to `ma-next/netlify/functions/_tools/actions/` for direct execution on Netlify servers.
- **Introspection**: The `scripts/nangoIntrospection/index.ts` script generates constants files (e.g., `ma-next/netlify/functions/_agents/nangoConstants.ts`) listing available Nango endpoints and their parameters, used by agents and workflows.

#### Integration Implementation Flow

- **Action Example (e.g., Google Calendar `create-event`)**:
  - Defined in `packages/emcpe-nango-integrations/google-calendar/nango.yaml`.
  - Implemented in `packages/emcpe-nango-integrations/google-calendar/google-calendar/actions/create-event.ts`.
  - Copied to `ma-next/netlify/functions/_tools/actions/google-calendar/create-event.ts` for server execution.
  - Input parameters defined in `ma-next/netlify/functions/_agents/nangoConstants.ts` via introspection.
  - UI configured in `ma-next/src/config/integrationUI.ts`.
  - Optional rich UI components in `ma-next/src/components/action-call/rich-parameters/` or `ma-next/src/components/action-call/rich-results/`.
- **Sync Example (e.g., Google Mail `emails-fork`)**:
  - Defined in `packages/emcpe-nango-integrations/google-mail/nango.yaml`.
  - Implemented in `packages/emcpe-nango-integrations/google-mail/syncs/emails-fork.ts`.
  - Runs on Nango servers, sending events to `ma-next/netlify/functions/sync/index.mts`.
  - Output parameters defined in `ma-next/netlify/functions/_agents/nangoConstants.ts` via introspection.

### Database Architecture

- **Schema Definition**: Defined in `packages/supabase/schema.prisma`, used to create the database schema.
- **Migration Strategy**: A single idempotent migration in `packages/supabase/migrations/functions_permissions_cron.sql`.
- **Schema Updates**: Applied by pushing with Prisma and re-running the idempotent migration.

### Workflow (aka Taskflows) Execution

- Taskflows are created in agent mode or triggered by external events via Nango.
- The sync webhook (`ma-next/netlify/functions/sync/index.mts`) receives events from Nango.
- The taskflow engine processes the taskflow schema's to complete taskflow executions.
- Actions are executed through Nango integrations or directly via Netlify functions.
- Results are stored in the database.

## Secondary Packages

- **mastra**: Agent orchestration package. Currently deprecated. We're moving to direct vercel-sdk implementations.
- **nango-integrations**: Combined Nango integration configurations for deployment.

## Key Relationships

The `nangoIntrospection` script bridges Nango integrations with the application by generating constants files that define available actions and syncs. These constants are used by:

- The UI in task mode to allow for connection setup.
- The UI in task mode to enable action execution.
- The taskflow engine in agent mode to create workflows.
- UI components to render integration-specific interfaces.

This architecture supports extensibility, allowing new integrations to be added by defining them in Nango YAML files, implementing their logic, and automatically making them available to agents and workflows through introspection.actions/create-event.ts`).

- **Syncs**: Defined in `packages/makeagent-nango-integrations` (e.g., `google-mail/syncs/emails-fork.ts`).
- **Deployment**:
  - Actions and syncs are combined into `packages/nango-integrations` for deployment to Nango.
  - Actions are copied to `ma-next/netlify/functions/_tools/actions/` for direct execution on Netlify servers.
- **Introspection**: The `scripts/nangoIntrospection/index.ts` script generates constants files (e.g., `ma-next/netlify/functions/_agents/nangoConstants.ts`) listing available Nango endpoints and their parameters, used by agents and workflows.

#### Integration Implementation Flow

- **Action Example (e.g., Google Calendar `create-event`)**:
  - Defined in `packages/emcpe-nango-integrations/google-calendar/nango.yaml`.
  - Implemented in `packages/emcpe-nango-integrations/google-calendar/actions/create-event.ts`.
  - Copied to `ma-next/netlify/functions/_tools/actions/google-calendar/create-event.ts` for server execution.
  - Input parameters defined in `ma-next/netlify/functions/_agents/nangoConstants.ts` via introspection.
  - UI configured in `ma-next/src/config/integrationUI.ts`.
  - Optional rich UI components in `ma-next/src/components/action-call/rich-parameters/` or `ma-next/src/components/action-call/rich-results/`.
- **Sync Example (e.g., Google Mail `emails-fork`)**:
  - Defined in `packages/makeagent-nango-integrations/nango.yaml`.
  - Implemented in `packages/makeagent-nango-integrations/google-mail/syncs/emails-fork.ts`.
  - Runs on Nango servers, sending events to `ma-next/netlify/functions/sync/index.mts`.
  - Output parameters defined in `ma-next/netlify/functions/_agents/nangoConstants.ts` via introspection.

### Database Architecture

- **Schema Definition**: Defined in `packages/supabase/schema.prisma`, used to create the database schema.
- **Migration Strategy**: A single idempotent migration in `packages/supabase/migrations/functions_permissions_cron.sql`.
- **Schema Updates**: Applied by pushing with Prisma and re-running the idempotent migration.

### Workflow (aka Taskflow) Execution - Agent mode

- Taskflows created in agent mode are triggered by external events e.g. Nango / cron.
- The sync webhook (`ma-next/netlify/functions/sync/index.mts`) receives events from Nango.
- The taskflow engine processes the workflow schema.
- Actions are executed through Nango integrations or directly via Netlify functions.
- Results are stored in the database and presented to the user.

## Secondary Packages

- **mastra**: Agent orchestration package.
- **ui**: Shared UI components.
- **nango-integrations**: Combined Nango integration configurations for deployment.

=====================================================================================================

Subject specific guides can be found in the prompts folder. E.g.

prompts/NANGO.md
prompts/RICH_UI.md

## STYLE GUIDE
Can be found in prompts/PROJECT.md



## TESTING

- We exclusively test with native node testing. Do not introduce any test libraries.
- We write our tests in typescript files.
- We do not run an entire suite of tests after each change, just pick and choose any tests for files you're specifically working on.
- Tests are run WITHOUT COMPILIATION BY RUNNING `tsx <path-to-.test.tsx>`

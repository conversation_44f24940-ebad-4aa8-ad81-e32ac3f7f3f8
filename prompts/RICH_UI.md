```markdown
# Instructions for Creating New Rich UI Components

These instructions guide you through creating new rich UI components for specific Nango actions using a list of `providerKey` and `actionKey` pairs, leveraging TypeScript types from `nangoModels.ts`.

## Objective
Create new rich UI components for specific Nango actions based on a provided list of `providerKey` and `actionKey` pairs, ensuring they use the correct TypeScript types from `nangoModels.ts`.

## Process

1. **Receive the List**
   - Obtain the list of `providerKey` and `actionKey` pairs for which new rich UI components are needed.

2. **Determine Component Type**
   - For each pair, decide if the component should display **parameters** (for actions) or **results** (for actions or syncs).
     - **Parameters**: Add to `packages/ma-next/src/components/rich-ui/RichParameterDisplay.tsx`, using the corresponding **INPUT** model.
     - **Results**: Add to `packages/ma-next/src/components/rich-ui/RichResultDisplay.tsx`, using the corresponding **OUTPUT** model.

3. **Find Model Name from Reference**
   - Read `packages/ma-next/src/constants/nangoReference.ts`.
     - For **parameter components**, iterate through the `ACTION_INPUTS` array to find the object where `provider` matches your `providerKey` and `action` matches your `actionKey`. The `model` property is the TypeScript model name to use.
     - For **result components**, iterate through the `ACTION_OUTPUTS` array similarly to find the `model` property.
   - **Note**: Typically, use a tool like `read_file` to access `nangoReference.ts`.

4. **Find TypeScript Interface**
   - Read `packages/ma-next/src/config/nangoModels.ts`.
   - Locate the `export interface` definition matching the model name from step 3. This defines the data structure for the component.
   - **Note**: Use a tool like `read_file` to access `nangoModels.ts`.

5. **Create New Component File**
   - Create a new `.tsx` file in the appropriate directory:
     - **Parameter components**: `packages/ma-next/src/components/rich-ui/rich-parameters/`
     - **Result components**: `packages/ma-next/src/components/rich-ui/rich-results/`
   - Name the file descriptively, e.g., `[Provider][Action]Display.tsx`.
   - Switch to **Code** mode and use the `write_to_file` tool to create the file with the initial component structure.
   - Import `React` and the relevant TypeScript model from `src/config/nangoModels.ts`. Define the component’s props interface using the model name from step 3.

   **Example Component Structure (Parameter Component)**:
   ```tsx
   import React from 'react';
   import { YourModelName } from 'src/config/nangoModels'; // Replace YourModelName

   type YourComponentDisplayProps = {
     parameters: YourModelName;
   }

   function YourComponentDisplay({ parameters }: YourComponentDisplayProps) {
     // Component logic to display parameters based on YourModelName structure
     // Refer to the interface definition from step 4 to access properties
     return (
       <div>
         {/* Render UI based on parameters */}
       </div>
     );
   }

   export { YourComponentDisplay };
   ```

   **Example Component Structure (Result Component)**:

   ```tsx
   import React from 'react';
   import { YourModelName } from 'src/config/nangoModels'; // Replace YourModelName

   type YourComponentDisplayProps = {
     output: YourModelName;
   }

   function YourComponentDisplay({ output }: YourComponentDisplayProps) {
     // Component logic to display output based on YourModelName structure
     // Refer to the interface definition from step 4 to access properties
     return (
       <div>
         {/* Render UI based on output */}
       </div>
     );
   }

   export { YourComponentDisplay };
   ```

6. **Implement Component Logic**
   - Fill in the component logic to display data from the `parameters` or `output` prop.
   - Refer to the TypeScript interface from step 4 to access properties (e.g., `parameters.propertyName`).
   - Design the UI to effectively present the information.

7. **Import and Add Component to Display Map**
   - Read the appropriate file:
     - For **parameter components**: `packages/ma-next/src/components/rich-ui/RichParameterDisplay.tsx`
     - For **result components**: `packages/ma-next/src/components/rich-ui/RichResultDisplay.tsx`
   - Use the `read_file` tool, switch to **Code** mode, and use `write_to_file` to modify the file.
   - Import the new component at the top of the file.
   - Add an entry to the `RICH_PARAM_COMPONENTS` or `RICH_RESULT_COMPONENTS` object, mapping the `providerKey:actionKey` string to the imported component.

   **Example Modification for `RichParameterDisplay.tsx`**:
   ```tsx
   import { GmailDraftDisplay } from './rich-parameters/GmailDraftDisplay';
   // ... other imports
   import { NewProviderNewActionDisplay } from './rich-parameters/NewProviderNewActionDisplay'; // Import new component

   const RICH_PARAM_COMPONENTS: Record<string, React.FC<{ parameters: any }>> = {
     'google-mail:compose-draft': GmailDraftDisplay,
     // ... other entries
     'new-provider:new-action': NewProviderNewActionDisplay, // Add new entry
   };

   // ... rest of the file
   ```

8. **Verify and Refine**
   - Review the new component and updated display file for correctness and type safety.
   - Adjust the component’s rendering logic based on the actual data structure and desired presentation.

## Rich UI Design Guidelines
To align with the desired direction for rich UI components, adhere to the following principles:
- **No IDs**: Do not display IDs to the user.
- **Context Menu**: Include a triple-dot/context menu as a standard feature.
- **No External Links on Main Page**: Avoid showing links to open externally on the main page.
- **Richer Success Data**: Utilize input data to enhance the success data displayed.
- **Logo and Title Bar**:
  - Prefer including a larger logo inside the panel.
  - Avoid repeating the panel title bar content next to the logo. The title bar should describe the entity’s context (e.g., *Google Sheet Created* or *Google Sheet Updated*), while the info next to the logo should be the entity’s name or title. If not available, use a fallback (e.g., *Spreadsheet Updated*).
```

{"name": "make-agent", "private": true, "version": "0.0.0", "type": "module", "workspaces": ["packages/*"], "scripts": {"introspect": "pnpm tsx scripts/nangoIntrospection/index.ts --force", "mastra": "pnpm --filter @makeagent/mastra run dev", "ni": "pnpm --filter @makeagent/nango-inspector run dev", "ei": "pnpm --filter @makeagent/executions-inspector run dev", "ma": "pnpm --filter @makeagent/ma-next run netlify", "connections": "npx tsx scripts/connections.ts", "connections-f": "npx tsx scripts/connections.ts --force", "connections-e": "npx tsx scripts/connections.ts --emails", "distributeEnv": "pnpm tsx scripts/distributeEnv.ts", "nango:deploy": "./scripts/nango-deploy.sh", "build:all": "pnpm -r run build", "build:mastra": "pnpm --filter @makeagent/mastra run build", "build:ma-next": "pnpm --filter @makeagent/ma-next run build", "pp": "pnpm prisma db push --schema=packages/supabase/schema.prisma", "ppsql": "npx tsx scripts/ppAndMigrate.ts", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,css,scss,md}\"", "push-netlify": "git subtree push --prefix=packages/ma-next **************:makeagent/makeagent-netlify.git main", "push-netlify-f": "git subtree split --prefix=packages/ma-next -b temp-branch && git push -f **************:makeagent/makeagent-netlify.git temp-branch:main && git branch -D temp-branch", "push-emcpen": "git subtree push --prefix=packages/emcpe-nango-integrations **************:makeagent/emcpe-nango.git main", "push-emcpen-f": "git subtree split --prefix=packages/emcpe-nango-integrations -b temp-branch && git push -f **************:makeagent/emcpe-nango.git temp-branch:main && git branch -D temp-branch"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@nangohq/node": "^0.58.7", "@prisma/client": "^6.4.1", "ai": "^4.3.16", "fast-glob": "^3.3.3", "js-yaml": "^4.1.0", "openai": "^4.98.0", "tsx": "^4.19.3", "zod": "^3.24.4"}, "devDependencies": {"@supabase/supabase-js": "^2.49.4", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.14", "@types/node": "^20.17.30", "@types/prompts": "^2.4.9", "dotenv": "^16.4.5", "esbuild": "^0.25.4", "eslint": "^9.9.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "fs-extra": "^11.3.0", "glob-promise": "^6.0.7", "prettier": "^3.2.5", "prisma": "^6.4.1", "prompts": "^2.4.2", "supabase": "^2.22.12", "typescript": "^5.5.3"}, "pnpm": {"onlyBuiltDependencies": ["@prisma/client", "@prisma/engines", "esbuild", "onnxruntime-node", "prisma", "protobufjs", "sharp", "supabase"]}}